# 壹人音 - 数据库设计文档

## 1. 数据库概述

### 数据库选择
- **主数据库**: MySQL 8.0
- **缓存数据库**: Redis 6.0
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci

### 设计原则
- 遵循第三范式，减少数据冗余
- 合理使用索引提升查询性能
- 预留扩展字段支持业务发展
- 统一命名规范和数据类型

## 2. 核心数据表设计

### 2.1 用户表 (users)
```sql
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
    nickname VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0-未知, 1-男, 2-女',
    age TINYINT DEFAULT NULL COMMENT '年龄',
    city VARCHAR(50) DEFAULT NULL COMMENT '城市',
    preferences JSON DEFAULT NULL COMMENT '音乐偏好(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

### 2.2 歌曲表 (songs)
```sql
CREATE TABLE songs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '歌曲ID',
    title VARCHAR(200) NOT NULL COMMENT '歌曲标题',
    artist VARCHAR(100) NOT NULL COMMENT '艺术家',
    album VARCHAR(200) DEFAULT NULL COMMENT '专辑名称',
    duration INT DEFAULT NULL COMMENT '时长(秒)',
    cover_url VARCHAR(500) DEFAULT NULL COMMENT '封面图片URL',
    audio_url VARCHAR(500) NOT NULL COMMENT '音频文件URL',
    genre VARCHAR(50) DEFAULT NULL COMMENT '音乐风格',
    mood VARCHAR(50) DEFAULT NULL COMMENT '情绪标签',
    tags JSON DEFAULT NULL COMMENT '标签(JSON数组)',
    play_count INT DEFAULT 0 COMMENT '播放次数',
    like_count INT DEFAULT 0 COMMENT '喜欢次数',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-下架, 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_artist (artist),
    INDEX idx_genre (genre),
    INDEX idx_mood (mood),
    INDEX idx_status (status),
    INDEX idx_play_count (play_count DESC)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歌曲表';
```

### 2.3 用户歌曲推荐记录表 (user_song_recommendations)
```sql
CREATE TABLE user_song_recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    song_id BIGINT NOT NULL COMMENT '歌曲ID',
    recommendation_type VARCHAR(50) NOT NULL COMMENT '推荐类型: daily, mood, preference',
    recommendation_score DECIMAL(3,2) DEFAULT NULL COMMENT '推荐分数(0-1)',
    is_played TINYINT DEFAULT 0 COMMENT '是否播放: 0-否, 1-是',
    is_liked TINYINT DEFAULT 0 COMMENT '是否喜欢: 0-否, 1-是',
    play_duration INT DEFAULT 0 COMMENT '播放时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '推荐时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_song_id (song_id),
    INDEX idx_recommendation_type (recommendation_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户歌曲推荐记录表';
```

### 2.4 问卷模板表 (survey_templates)
```sql
CREATE TABLE survey_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '问卷模板ID',
    name VARCHAR(100) NOT NULL COMMENT '问卷名称',
    description TEXT DEFAULT NULL COMMENT '问卷描述',
    questions JSON NOT NULL COMMENT '问题配置(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷模板表';
```

### 2.5 用户问卷回答表 (user_survey_responses)
```sql
CREATE TABLE user_survey_responses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '回答记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    song_id BIGINT NOT NULL COMMENT '歌曲ID',
    template_id BIGINT NOT NULL COMMENT '问卷模板ID',
    responses JSON NOT NULL COMMENT '回答内容(JSON格式)',
    overall_rating TINYINT DEFAULT NULL COMMENT '总体评分(1-5)',
    completion_time INT DEFAULT NULL COMMENT '完成时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_song_id (song_id),
    INDEX idx_template_id (template_id),
    INDEX idx_overall_rating (overall_rating),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES survey_templates(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户问卷回答表';
```

### 2.6 管理员表 (admins)
```sql
CREATE TABLE admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    role VARCHAR(20) DEFAULT 'admin' COMMENT '角色: admin, super_admin',
    permissions JSON DEFAULT NULL COMMENT '权限配置',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';
```

## 3. 数据字典

### 3.1 用户性别枚举
- 0: 未知
- 1: 男性  
- 2: 女性

### 3.2 推荐类型枚举
- daily: 每日推荐
- mood: 情绪推荐
- preference: 偏好推荐
- random: 随机推荐

### 3.3 音乐风格分类
- pop: 流行
- rock: 摇滚
- jazz: 爵士
- classical: 古典
- electronic: 电子
- folk: 民谣
- hiphop: 嘻哈
- country: 乡村

### 3.4 情绪标签
- happy: 快乐
- sad: 悲伤
- calm: 平静
- energetic: 充满活力
- romantic: 浪漫
- nostalgic: 怀旧
- mysterious: 神秘

## 4. 索引优化策略

### 4.1 查询优化索引
- 用户手机号查询: `idx_phone`
- 歌曲播放排行: `idx_play_count`
- 推荐记录查询: `idx_user_id`, `idx_created_at`
- 问卷数据分析: `idx_overall_rating`, `idx_template_id`

### 4.2 复合索引
```sql
-- 用户推荐历史查询
ALTER TABLE user_song_recommendations 
ADD INDEX idx_user_created (user_id, created_at);

-- 歌曲风格情绪查询
ALTER TABLE songs 
ADD INDEX idx_genre_mood (genre, mood, status);
```

## 5. 数据备份策略

### 5.1 备份计划
- **全量备份**: 每日凌晨2点
- **增量备份**: 每4小时一次
- **备份保留**: 30天内的所有备份

### 5.2 恢复策略
- **RTO**: 恢复时间目标 < 1小时
- **RPO**: 恢复点目标 < 4小时
- **测试**: 每月进行恢复演练
