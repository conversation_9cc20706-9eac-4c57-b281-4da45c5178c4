# 壹人音后端环境配置

# 服务器配置
NODE_ENV=development
PORT=3001

# 前端地址
FRONTEND_URL=http://localhost:5173

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_NAME=yiren_music
DB_USER=root
DB_PASSWORD=your_password

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=your_super_secret_jwt_key_here
JWT_EXPIRES_IN=7d

# 文件上传配置
UPLOAD_PATH=./uploads
MAX_FILE_SIZE=10485760

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# 第三方服务
MUSIC_API_KEY=your_music_api_key
ANALYTICS_KEY=your_analytics_key

# 安全配置
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
