/**
 * 用户认证路由
 * 功能：登录、注册、手机验证、JWT管理
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const router = express.Router();

// 模拟数据库（实际项目中应该使用真实数据库）
let users = [
  {
    id: 1,
    phone: '13911091416',
    nickname: '音乐爱好者',
    password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', // password123
    preferences: {
      genres: ['ambient', 'electronic'],
      moods: ['mysterious', 'calm']
    },
    createdAt: new Date().toISOString()
  }
];

/**
 * 手机号登录/注册
 * POST /api/auth/phone-login
 */
router.post('/phone-login', async (req, res) => {
  try {
    const { phone, verificationCode } = req.body;

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 模拟验证码验证（实际项目中应该验证真实验证码）
    if (verificationCode !== '1234') {
      return res.status(400).json({
        success: false,
        message: '验证码错误'
      });
    }

    // 查找或创建用户
    let user = users.find(u => u.phone === phone);
    
    if (!user) {
      // 新用户注册
      user = {
        id: users.length + 1,
        phone,
        nickname: `用户${phone.slice(-4)}`,
        preferences: {
          genres: [],
          moods: []
        },
        createdAt: new Date().toISOString()
      };
      users.push(user);
    }

    // 生成JWT token
    const token = jwt.sign(
      { 
        userId: user.id, 
        phone: user.phone 
      },
      process.env.JWT_SECRET || 'default_secret',
      { 
        expiresIn: process.env.JWT_EXPIRES_IN || '7d' 
      }
    );

    // 返回用户信息和token
    res.json({
      success: true,
      message: user.id === users.length ? '注册成功' : '登录成功',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          preferences: user.preferences
        },
        token
      }
    });

  } catch (error) {
    console.error('Phone login error:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 发送验证码
 * POST /api/auth/send-code
 */
router.post('/send-code', async (req, res) => {
  try {
    const { phone } = req.body;

    // 验证手机号格式
    if (!/^1[3-9]\d{9}$/.test(phone)) {
      return res.status(400).json({
        success: false,
        message: '手机号格式不正确'
      });
    }

    // 模拟发送验证码（实际项目中应该调用短信服务）
    console.log(`发送验证码到 ${phone}: 1234`);

    res.json({
      success: true,
      message: '验证码已发送',
      data: {
        phone,
        expiresIn: 300 // 5分钟过期
      }
    });

  } catch (error) {
    console.error('Send code error:', error);
    res.status(500).json({
      success: false,
      message: '发送验证码失败'
    });
  }
});

/**
 * 验证token
 * POST /api/auth/verify-token
 */
router.post('/verify-token', async (req, res) => {
  try {
    const { token } = req.body;

    if (!token) {
      return res.status(400).json({
        success: false,
        message: 'Token不能为空'
      });
    }

    // 验证JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'default_secret');
    const user = users.find(u => u.id === decoded.userId);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: 'Token有效',
      data: {
        user: {
          id: user.id,
          phone: user.phone,
          nickname: user.nickname,
          preferences: user.preferences
        }
      }
    });

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({
        success: false,
        message: 'Token无效'
      });
    }
    
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({
        success: false,
        message: 'Token已过期'
      });
    }

    console.error('Verify token error:', error);
    res.status(500).json({
      success: false,
      message: '服务器错误'
    });
  }
});

/**
 * 退出登录
 * POST /api/auth/logout
 */
router.post('/logout', (req, res) => {
  // 客户端删除token即可实现退出
  res.json({
    success: true,
    message: '退出成功'
  });
});

module.exports = router;
