/**
 * 用户管理路由
 * 功能：用户信息管理、偏好设置、用户统计
 */

const express = require('express');
const router = express.Router();

// 模拟用户数据（实际项目中应该从数据库获取）
let users = [
  {
    id: 1,
    phone: '13911091416',
    nickname: '音乐爱好者',
    avatar: '/uploads/avatars/default.jpg',
    gender: 0,
    age: 25,
    city: '北京',
    preferences: {
      genres: ['ambient', 'electronic'],
      moods: ['mysterious', 'calm']
    },
    status: 1,
    lastLoginAt: new Date().toISOString(),
    createdAt: '2024-01-01T00:00:00Z'
  }
];

/**
 * 获取用户信息
 * GET /api/users/:id
 */
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const user = users.find(u => u.id === parseInt(id) && u.status === 1);

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 不返回敏感信息
    const { password, ...userInfo } = user;

    res.json({
      success: true,
      data: { user: userInfo }
    });

  } catch (error) {
    console.error('Get user error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户信息失败'
    });
  }
});

/**
 * 更新用户信息
 * PUT /api/users/:id
 */
router.put('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const { nickname, avatar, gender, age, city } = req.body;

    const userIndex = users.findIndex(u => u.id === parseInt(id));
    
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 更新用户信息
    const updatedFields = {};
    if (nickname !== undefined) updatedFields.nickname = nickname;
    if (avatar !== undefined) updatedFields.avatar = avatar;
    if (gender !== undefined) updatedFields.gender = parseInt(gender);
    if (age !== undefined) updatedFields.age = parseInt(age);
    if (city !== undefined) updatedFields.city = city;

    users[userIndex] = {
      ...users[userIndex],
      ...updatedFields,
      updatedAt: new Date().toISOString()
    };

    const { password, ...userInfo } = users[userIndex];

    res.json({
      success: true,
      message: '用户信息更新成功',
      data: { user: userInfo }
    });

  } catch (error) {
    console.error('Update user error:', error);
    res.status(500).json({
      success: false,
      message: '更新用户信息失败'
    });
  }
});

/**
 * 更新用户音乐偏好
 * PUT /api/users/:id/preferences
 */
router.put('/:id/preferences', (req, res) => {
  try {
    const { id } = req.params;
    const { genres, moods } = req.body;

    const userIndex = users.findIndex(u => u.id === parseInt(id));
    
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 验证偏好数据格式
    if (genres && !Array.isArray(genres)) {
      return res.status(400).json({
        success: false,
        message: '音乐类型偏好必须是数组格式'
      });
    }

    if (moods && !Array.isArray(moods)) {
      return res.status(400).json({
        success: false,
        message: '情绪偏好必须是数组格式'
      });
    }

    // 更新偏好设置
    const updatedPreferences = {
      ...users[userIndex].preferences
    };

    if (genres !== undefined) updatedPreferences.genres = genres;
    if (moods !== undefined) updatedPreferences.moods = moods;

    users[userIndex] = {
      ...users[userIndex],
      preferences: updatedPreferences,
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '音乐偏好更新成功',
      data: { 
        preferences: updatedPreferences 
      }
    });

  } catch (error) {
    console.error('Update user preferences error:', error);
    res.status(500).json({
      success: false,
      message: '更新音乐偏好失败'
    });
  }
});

/**
 * 获取用户统计信息
 * GET /api/users/:id/stats
 */
router.get('/:id/stats', (req, res) => {
  try {
    const { id } = req.params;
    const user = users.find(u => u.id === parseInt(id));

    if (!user) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 模拟统计数据（实际项目中应该从数据库查询）
    const stats = {
      totalListeningTime: 12450, // 总听歌时长（分钟）
      totalSongs: 89, // 听过的歌曲数量
      favoriteGenre: 'ambient', // 最喜欢的音乐类型
      favoriteMood: 'mysterious', // 最喜欢的情绪
      averageRating: 4.2, // 平均评分
      totalSurveys: 15, // 完成的问卷数量
      joinDays: Math.floor((new Date() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24)), // 加入天数
      lastActiveDate: user.lastLoginAt
    };

    res.json({
      success: true,
      data: { stats }
    });

  } catch (error) {
    console.error('Get user stats error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户统计失败'
    });
  }
});

/**
 * 获取用户列表（管理员功能）
 * GET /api/users
 */
router.get('/', (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      search, 
      status,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let filteredUsers = [...users];

    // 状态筛选
    if (status !== undefined) {
      filteredUsers = filteredUsers.filter(user => user.status === parseInt(status));
    }

    // 搜索功能
    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user => 
        user.phone.includes(search) ||
        (user.nickname && user.nickname.toLowerCase().includes(searchLower)) ||
        (user.city && user.city.toLowerCase().includes(searchLower))
      );
    }

    // 排序
    filteredUsers.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    // 移除敏感信息
    const safeUsers = paginatedUsers.map(user => {
      const { password, ...safeUser } = user;
      return safeUser;
    });

    res.json({
      success: true,
      data: {
        users: safeUsers,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredUsers.length,
          totalPages: Math.ceil(filteredUsers.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get users error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败'
    });
  }
});

/**
 * 删除用户（软删除）
 * DELETE /api/users/:id
 */
router.delete('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const userIndex = users.findIndex(u => u.id === parseInt(id));
    
    if (userIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 软删除：将状态设为0
    users[userIndex] = {
      ...users[userIndex],
      status: 0,
      updatedAt: new Date().toISOString()
    };

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('Delete user error:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败'
    });
  }
});

module.exports = router;
