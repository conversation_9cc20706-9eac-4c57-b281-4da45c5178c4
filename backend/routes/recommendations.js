/**
 * 歌曲推荐路由
 * 功能：个性化推荐、每日推荐、情绪推荐、推荐算法
 */

const express = require('express');
const router = express.Router();

// 导入歌曲数据（实际项目中应该从数据库获取）
const songs = [
  {
    id: 1,
    title: 'Eye of Horus',
    artist: 'Desert Mystic',
    album: 'Ancient Winds',
    duration: 240,
    coverUrl: '/uploads/covers/eye-of-horus.jpg',
    audioUrl: '/uploads/audio/eye-of-horus.mp3',
    genre: 'ambient',
    mood: 'mysterious',
    tags: ['沙漠', '神秘', '古埃及', '冥想'],
    playCount: 1250,
    likeCount: 89
  },
  {
    id: 2,
    title: 'Neon Dreams',
    artist: 'Cyber Wave',
    album: 'Future Sounds',
    duration: 180,
    coverUrl: '/uploads/covers/neon-dreams.jpg',
    audioUrl: '/uploads/audio/neon-dreams.mp3',
    genre: 'electronic',
    mood: 'energetic',
    tags: ['科技', '未来', '电子', '活力'],
    playCount: 2100,
    likeCount: 156
  },
  {
    id: 3,
    title: 'Peaceful Morning',
    artist: 'Nature Harmony',
    album: 'Calm Collection',
    duration: 300,
    coverUrl: '/uploads/covers/peaceful-morning.jpg',
    audioUrl: '/uploads/audio/peaceful-morning.mp3',
    genre: 'ambient',
    mood: 'calm',
    tags: ['自然', '平静', '早晨', '放松'],
    playCount: 890,
    likeCount: 67
  },
  {
    id: 4,
    title: 'Urban Pulse',
    artist: 'City Beats',
    album: 'Metropolitan',
    duration: 210,
    coverUrl: '/uploads/covers/urban-pulse.jpg',
    audioUrl: '/uploads/audio/urban-pulse.mp3',
    genre: 'electronic',
    mood: 'energetic',
    tags: ['城市', '节拍', '现代', '动感'],
    playCount: 1680,
    likeCount: 124
  },
  {
    id: 5,
    title: 'Moonlight Serenade',
    artist: 'Luna Ensemble',
    album: 'Night Whispers',
    duration: 270,
    coverUrl: '/uploads/covers/moonlight-serenade.jpg',
    audioUrl: '/uploads/audio/moonlight-serenade.mp3',
    genre: 'ambient',
    mood: 'romantic',
    tags: ['月光', '浪漫', '夜晚', '温柔'],
    playCount: 1420,
    likeCount: 98
  }
];

// 模拟用户推荐历史
let recommendationHistory = [];

/**
 * 智能推荐算法
 * 基于用户偏好、时间、历史行为等因素
 */
function getRecommendationScore(song, userPreferences, timeOfDay, userHistory) {
  let score = 0;

  // 基础分数（播放次数和点赞数）
  score += (song.playCount / 1000) * 0.3;
  score += (song.likeCount / 100) * 0.2;

  // 用户偏好匹配
  if (userPreferences.genres && userPreferences.genres.includes(song.genre)) {
    score += 0.4;
  }
  if (userPreferences.moods && userPreferences.moods.includes(song.mood)) {
    score += 0.3;
  }

  // 时间因素
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 12 && song.mood === 'energetic') {
    score += 0.2; // 早晨推荐活力歌曲
  } else if (hour >= 12 && hour < 18 && song.mood === 'calm') {
    score += 0.1; // 下午推荐平静歌曲
  } else if (hour >= 18 && (song.mood === 'romantic' || song.mood === 'mysterious')) {
    score += 0.2; // 晚上推荐浪漫或神秘歌曲
  }

  // 避免重复推荐
  const recentlyRecommended = userHistory.some(h => 
    h.songId === song.id && 
    new Date(h.createdAt) > new Date(Date.now() - 24 * 60 * 60 * 1000)
  );
  if (recentlyRecommended) {
    score -= 0.5;
  }

  return Math.max(0, score);
}

/**
 * 获取个性化推荐
 * POST /api/recommendations/personalized
 */
router.post('/personalized', (req, res) => {
  try {
    const { userId, userPreferences = {}, limit = 1 } = req.body;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    // 获取用户历史推荐记录
    const userHistory = recommendationHistory.filter(h => h.userId === userId);

    // 计算每首歌的推荐分数
    const scoredSongs = songs.map(song => ({
      ...song,
      recommendationScore: getRecommendationScore(song, userPreferences, new Date(), userHistory)
    }));

    // 按分数排序并选择推荐歌曲
    const recommendedSongs = scoredSongs
      .sort((a, b) => b.recommendationScore - a.recommendationScore)
      .slice(0, parseInt(limit));

    // 记录推荐历史
    recommendedSongs.forEach(song => {
      recommendationHistory.push({
        id: recommendationHistory.length + 1,
        userId,
        songId: song.id,
        recommendationType: 'personalized',
        recommendationScore: song.recommendationScore,
        createdAt: new Date().toISOString()
      });
    });

    res.json({
      success: true,
      message: '获取个性化推荐成功',
      data: {
        recommendations: recommendedSongs.map(song => ({
          ...song,
          recommendationType: 'personalized',
          recommendationReason: '基于您的音乐偏好推荐'
        }))
      }
    });

  } catch (error) {
    console.error('Personalized recommendation error:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐失败'
    });
  }
});

/**
 * 获取每日推荐
 * GET /api/recommendations/daily
 */
router.get('/daily', (req, res) => {
  try {
    const { userId, limit = 5 } = req.query;

    // 基于日期的伪随机推荐（确保同一天推荐相同）
    const today = new Date().toDateString();
    const seed = today.split('').reduce((a, b) => a + b.charCodeAt(0), 0);
    
    // 使用种子随机数确保每日推荐的一致性
    const shuffledSongs = [...songs].sort(() => {
      const x = Math.sin(seed++) * 10000;
      return x - Math.floor(x) - 0.5;
    });

    const dailyRecommendations = shuffledSongs.slice(0, parseInt(limit));

    // 记录推荐历史
    if (userId) {
      dailyRecommendations.forEach(song => {
        recommendationHistory.push({
          id: recommendationHistory.length + 1,
          userId: parseInt(userId),
          songId: song.id,
          recommendationType: 'daily',
          recommendationScore: 0.8,
          createdAt: new Date().toISOString()
        });
      });
    }

    res.json({
      success: true,
      message: '获取每日推荐成功',
      data: {
        recommendations: dailyRecommendations.map(song => ({
          ...song,
          recommendationType: 'daily',
          recommendationReason: '今日精选推荐'
        })),
        date: today
      }
    });

  } catch (error) {
    console.error('Daily recommendation error:', error);
    res.status(500).json({
      success: false,
      message: '获取每日推荐失败'
    });
  }
});

/**
 * 基于情绪的推荐
 * GET /api/recommendations/mood/:mood
 */
router.get('/mood/:mood', (req, res) => {
  try {
    const { mood } = req.params;
    const { userId, limit = 3 } = req.query;

    // 筛选符合情绪的歌曲
    const moodSongs = songs.filter(song => song.mood === mood);

    if (moodSongs.length === 0) {
      return res.status(404).json({
        success: false,
        message: `没有找到${mood}情绪的歌曲`
      });
    }

    // 按播放次数和点赞数排序
    const sortedMoodSongs = moodSongs
      .sort((a, b) => (b.playCount + b.likeCount) - (a.playCount + a.likeCount))
      .slice(0, parseInt(limit));

    // 记录推荐历史
    if (userId) {
      sortedMoodSongs.forEach(song => {
        recommendationHistory.push({
          id: recommendationHistory.length + 1,
          userId: parseInt(userId),
          songId: song.id,
          recommendationType: 'mood',
          recommendationScore: 0.7,
          createdAt: new Date().toISOString()
        });
      });
    }

    res.json({
      success: true,
      message: `获取${mood}情绪推荐成功`,
      data: {
        recommendations: sortedMoodSongs.map(song => ({
          ...song,
          recommendationType: 'mood',
          recommendationReason: `符合您的${mood}情绪`
        })),
        mood
      }
    });

  } catch (error) {
    console.error('Mood recommendation error:', error);
    res.status(500).json({
      success: false,
      message: '获取情绪推荐失败'
    });
  }
});

/**
 * 获取推荐历史
 * GET /api/recommendations/history
 */
router.get('/history', (req, res) => {
  try {
    const { userId, page = 1, limit = 20 } = req.query;

    if (!userId) {
      return res.status(400).json({
        success: false,
        message: '用户ID不能为空'
      });
    }

    // 获取用户推荐历史
    const userRecommendations = recommendationHistory
      .filter(h => h.userId === parseInt(userId))
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedHistory = userRecommendations.slice(startIndex, endIndex);

    // 补充歌曲信息
    const historyWithSongs = paginatedHistory.map(rec => {
      const song = songs.find(s => s.id === rec.songId);
      return {
        ...rec,
        song
      };
    });

    res.json({
      success: true,
      data: {
        history: historyWithSongs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: userRecommendations.length,
          totalPages: Math.ceil(userRecommendations.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get recommendation history error:', error);
    res.status(500).json({
      success: false,
      message: '获取推荐历史失败'
    });
  }
});

module.exports = router;
