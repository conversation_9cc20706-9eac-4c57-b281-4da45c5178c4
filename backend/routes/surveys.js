/**
 * 问卷调研路由
 * 功能：问卷模板管理、用户回答收集、数据分析
 */

const express = require('express');
const router = express.Router();

// 模拟问卷模板数据
let surveyTemplates = [
  {
    id: 1,
    name: '歌曲体验问卷',
    description: '用于收集用户对推荐歌曲的反馈',
    questions: [
      {
        id: 1,
        type: 'rating',
        title: '喜欢这个曲子吗？',
        options: ['A', 'B', 'C'],
        required: true
      },
      {
        id: 2,
        type: 'multiple_choice',
        title: '音响设备评价',
        options: ['音质很好', '音量适中', '音效清晰', '有杂音'],
        required: true
      },
      {
        id: 3,
        type: 'rating',
        title: '环境体验评分',
        scale: 5,
        required: true
      },
      {
        id: 4,
        type: 'rating',
        title: '技师服务评价',
        scale: 5,
        required: true
      },
      {
        id: 5,
        type: 'rating',
        title: '整体满意度评分',
        scale: 5,
        required: true
      },
      {
        id: 6,
        type: 'text',
        title: '改进建议',
        required: false
      }
    ],
    status: 1,
    createdAt: '2024-01-01T00:00:00Z'
  }
];

// 模拟用户问卷回答数据
let surveyResponses = [];

/**
 * 获取问卷模板
 * GET /api/surveys/templates
 */
router.get('/templates', (req, res) => {
  try {
    const { status = 1 } = req.query;

    const activeTemplates = surveyTemplates.filter(template => 
      template.status === parseInt(status)
    );

    res.json({
      success: true,
      data: { templates: activeTemplates }
    });

  } catch (error) {
    console.error('Get survey templates error:', error);
    res.status(500).json({
      success: false,
      message: '获取问卷模板失败'
    });
  }
});

/**
 * 获取特定问卷模板
 * GET /api/surveys/templates/:id
 */
router.get('/templates/:id', (req, res) => {
  try {
    const { id } = req.params;
    const template = surveyTemplates.find(t => 
      t.id === parseInt(id) && t.status === 1
    );

    if (!template) {
      return res.status(404).json({
        success: false,
        message: '问卷模板不存在'
      });
    }

    res.json({
      success: true,
      data: { template }
    });

  } catch (error) {
    console.error('Get survey template error:', error);
    res.status(500).json({
      success: false,
      message: '获取问卷模板失败'
    });
  }
});

/**
 * 提交问卷回答
 * POST /api/surveys/responses
 */
router.post('/responses', (req, res) => {
  try {
    const { 
      userId, 
      songId, 
      templateId, 
      responses, 
      completionTime 
    } = req.body;

    // 验证必填字段
    if (!userId || !songId || !templateId || !responses) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 验证问卷模板是否存在
    const template = surveyTemplates.find(t => t.id === parseInt(templateId));
    if (!template) {
      return res.status(404).json({
        success: false,
        message: '问卷模板不存在'
      });
    }

    // 验证回答格式
    const requiredQuestions = template.questions.filter(q => q.required);
    const answeredQuestions = Object.keys(responses);
    
    for (const question of requiredQuestions) {
      if (!answeredQuestions.includes(question.id.toString())) {
        return res.status(400).json({
          success: false,
          message: `问题 "${question.title}" 为必填项`
        });
      }
    }

    // 计算总体评分（基于评分类问题的平均值）
    const ratingQuestions = template.questions.filter(q => q.type === 'rating' && q.scale);
    let totalRating = 0;
    let ratingCount = 0;

    for (const question of ratingQuestions) {
      const answer = responses[question.id];
      if (answer && !isNaN(answer)) {
        totalRating += parseInt(answer);
        ratingCount++;
      }
    }

    const overallRating = ratingCount > 0 ? Math.round(totalRating / ratingCount) : null;

    // 创建回答记录
    const response = {
      id: surveyResponses.length + 1,
      userId: parseInt(userId),
      songId: parseInt(songId),
      templateId: parseInt(templateId),
      responses,
      overallRating,
      completionTime: completionTime || null,
      createdAt: new Date().toISOString()
    };

    surveyResponses.push(response);

    res.json({
      success: true,
      message: '问卷提交成功',
      data: { 
        responseId: response.id,
        overallRating: response.overallRating
      }
    });

  } catch (error) {
    console.error('Submit survey response error:', error);
    res.status(500).json({
      success: false,
      message: '提交问卷失败'
    });
  }
});

/**
 * 获取用户问卷回答历史
 * GET /api/surveys/responses/user/:userId
 */
router.get('/responses/user/:userId', (req, res) => {
  try {
    const { userId } = req.params;
    const { page = 1, limit = 20 } = req.query;

    const userResponses = surveyResponses
      .filter(response => response.userId === parseInt(userId))
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedResponses = userResponses.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        responses: paginatedResponses,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: userResponses.length,
          totalPages: Math.ceil(userResponses.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get user survey responses error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户问卷历史失败'
    });
  }
});

/**
 * 获取歌曲问卷统计
 * GET /api/surveys/stats/song/:songId
 */
router.get('/stats/song/:songId', (req, res) => {
  try {
    const { songId } = req.params;

    const songResponses = surveyResponses.filter(
      response => response.songId === parseInt(songId)
    );

    if (songResponses.length === 0) {
      return res.json({
        success: true,
        data: {
          songId: parseInt(songId),
          totalResponses: 0,
          averageRating: null,
          ratingDistribution: {},
          commonFeedback: []
        }
      });
    }

    // 计算平均评分
    const ratingsWithValue = songResponses.filter(r => r.overallRating !== null);
    const averageRating = ratingsWithValue.length > 0 
      ? ratingsWithValue.reduce((sum, r) => sum + r.overallRating, 0) / ratingsWithValue.length
      : null;

    // 评分分布
    const ratingDistribution = {};
    ratingsWithValue.forEach(response => {
      const rating = response.overallRating;
      ratingDistribution[rating] = (ratingDistribution[rating] || 0) + 1;
    });

    // 收集文本反馈（改进建议）
    const textFeedback = songResponses
      .map(response => response.responses['6']) // 问题6是改进建议
      .filter(feedback => feedback && feedback.trim().length > 0);

    res.json({
      success: true,
      data: {
        songId: parseInt(songId),
        totalResponses: songResponses.length,
        averageRating: averageRating ? Math.round(averageRating * 100) / 100 : null,
        ratingDistribution,
        commonFeedback: textFeedback.slice(0, 10) // 返回最近10条反馈
      }
    });

  } catch (error) {
    console.error('Get song survey stats error:', error);
    res.status(500).json({
      success: false,
      message: '获取歌曲问卷统计失败'
    });
  }
});

/**
 * 获取问卷整体统计
 * GET /api/surveys/stats/overview
 */
router.get('/stats/overview', (req, res) => {
  try {
    const { startDate, endDate } = req.query;

    let filteredResponses = surveyResponses;

    // 日期筛选
    if (startDate) {
      filteredResponses = filteredResponses.filter(
        response => new Date(response.createdAt) >= new Date(startDate)
      );
    }
    if (endDate) {
      filteredResponses = filteredResponses.filter(
        response => new Date(response.createdAt) <= new Date(endDate)
      );
    }

    // 计算统计数据
    const totalResponses = filteredResponses.length;
    const ratingsWithValue = filteredResponses.filter(r => r.overallRating !== null);
    const averageRating = ratingsWithValue.length > 0 
      ? ratingsWithValue.reduce((sum, r) => sum + r.overallRating, 0) / ratingsWithValue.length
      : null;

    // 按日期分组统计
    const dailyStats = {};
    filteredResponses.forEach(response => {
      const date = response.createdAt.split('T')[0];
      if (!dailyStats[date]) {
        dailyStats[date] = { count: 0, totalRating: 0, ratingCount: 0 };
      }
      dailyStats[date].count++;
      if (response.overallRating !== null) {
        dailyStats[date].totalRating += response.overallRating;
        dailyStats[date].ratingCount++;
      }
    });

    // 计算每日平均评分
    Object.keys(dailyStats).forEach(date => {
      const stats = dailyStats[date];
      stats.averageRating = stats.ratingCount > 0 
        ? stats.totalRating / stats.ratingCount 
        : null;
    });

    res.json({
      success: true,
      data: {
        totalResponses,
        averageRating: averageRating ? Math.round(averageRating * 100) / 100 : null,
        dailyStats,
        period: {
          startDate: startDate || null,
          endDate: endDate || null
        }
      }
    });

  } catch (error) {
    console.error('Get survey overview stats error:', error);
    res.status(500).json({
      success: false,
      message: '获取问卷统计概览失败'
    });
  }
});

module.exports = router;
