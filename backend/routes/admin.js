/**
 * 管理员路由
 * 功能：管理后台、数据统计、系统管理
 */

const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const router = express.Router();

// 模拟管理员数据
let admins = [
  {
    id: 1,
    username: 'admin',
    password: '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G', // admin123
    realName: '系统管理员',
    email: '<EMAIL>',
    role: 'super_admin',
    permissions: ['user_manage', 'song_manage', 'survey_manage', 'data_analysis', 'system_config'],
    status: 1,
    lastLoginAt: null,
    createdAt: '2024-01-01T00:00:00Z'
  }
];

/**
 * 管理员登录
 * POST /api/admin/login
 */
router.post('/login', async (req, res) => {
  try {
    const { username, password } = req.body;

    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查找管理员
    const admin = admins.find(a => a.username === username && a.status === 1);
    
    if (!admin) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 验证密码
    const isPasswordValid = await bcrypt.compare(password, admin.password);
    
    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 更新最后登录时间
    const adminIndex = admins.findIndex(a => a.id === admin.id);
    admins[adminIndex].lastLoginAt = new Date().toISOString();

    // 生成JWT token
    const token = jwt.sign(
      { 
        adminId: admin.id, 
        username: admin.username,
        role: admin.role 
      },
      process.env.JWT_SECRET || 'default_secret',
      { 
        expiresIn: process.env.JWT_EXPIRES_IN || '7d' 
      }
    );

    // 返回管理员信息和token
    const { password: _, ...adminInfo } = admin;
    
    res.json({
      success: true,
      message: '登录成功',
      data: {
        admin: {
          ...adminInfo,
          lastLoginAt: admins[adminIndex].lastLoginAt
        },
        token
      }
    });

  } catch (error) {
    console.error('Admin login error:', error);
    res.status(500).json({
      success: false,
      message: '登录失败'
    });
  }
});

/**
 * 获取系统统计概览
 * GET /api/admin/dashboard/stats
 */
router.get('/dashboard/stats', (req, res) => {
  try {
    // 模拟统计数据（实际项目中应该从数据库查询）
    const stats = {
      totalUsers: 1250,
      activeUsers: 890,
      totalSongs: 156,
      totalPlays: 45680,
      totalSurveys: 2340,
      avgRating: 4.2,
      newUsersToday: 23,
      playsToday: 1280,
      surveysToday: 45,
      systemUptime: process.uptime(),
      lastUpdated: new Date().toISOString()
    };

    // 模拟趋势数据（最近7天）
    const trends = {
      userGrowth: [12, 18, 25, 31, 28, 35, 23],
      playTrends: [1200, 1350, 1180, 1420, 1380, 1450, 1280],
      ratingTrends: [4.1, 4.2, 4.0, 4.3, 4.2, 4.4, 4.2]
    };

    res.json({
      success: true,
      data: {
        stats,
        trends,
        period: 'last_7_days'
      }
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败'
    });
  }
});

/**
 * 获取用户分析数据
 * GET /api/admin/analytics/users
 */
router.get('/analytics/users', (req, res) => {
  try {
    const { period = '7d' } = req.query;

    // 模拟用户分析数据
    const analytics = {
      userDistribution: {
        byGender: { male: 45, female: 52, unknown: 3 },
        byAge: { 
          '18-25': 35, 
          '26-35': 40, 
          '36-45': 20, 
          '46+': 5 
        },
        byCity: {
          '北京': 25,
          '上海': 22,
          '广州': 18,
          '深圳': 15,
          '其他': 20
        }
      },
      preferences: {
        genres: {
          'ambient': 35,
          'electronic': 28,
          'pop': 20,
          'rock': 12,
          'jazz': 5
        },
        moods: {
          'calm': 30,
          'energetic': 25,
          'mysterious': 20,
          'romantic': 15,
          'happy': 10
        }
      },
      activity: {
        dailyActiveUsers: [120, 135, 128, 142, 138, 145, 128],
        avgSessionDuration: 25.5, // 分钟
        avgSongsPerSession: 3.2
      }
    };

    res.json({
      success: true,
      data: {
        analytics,
        period,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Get user analytics error:', error);
    res.status(500).json({
      success: false,
      message: '获取用户分析数据失败'
    });
  }
});

/**
 * 获取歌曲分析数据
 * GET /api/admin/analytics/songs
 */
router.get('/analytics/songs', (req, res) => {
  try {
    const { period = '7d' } = req.query;

    // 模拟歌曲分析数据
    const analytics = {
      topSongs: [
        { id: 2, title: 'Neon Dreams', artist: 'Cyber Wave', plays: 2100, rating: 4.5 },
        { id: 4, title: 'Urban Pulse', artist: 'City Beats', plays: 1680, rating: 4.3 },
        { id: 5, title: 'Moonlight Serenade', artist: 'Luna Ensemble', plays: 1420, rating: 4.4 },
        { id: 1, title: 'Eye of Horus', artist: 'Desert Mystic', plays: 1250, rating: 4.2 },
        { id: 3, title: 'Peaceful Morning', artist: 'Nature Harmony', plays: 890, rating: 4.1 }
      ],
      genreStats: {
        'electronic': { plays: 3780, avgRating: 4.4, songCount: 2 },
        'ambient': { plays: 3560, avgRating: 4.2, songCount: 3 }
      },
      moodStats: {
        'energetic': { plays: 3780, avgRating: 4.4 },
        'mysterious': { plays: 1250, avgRating: 4.2 },
        'calm': { plays: 890, avgRating: 4.1 },
        'romantic': { plays: 1420, avgRating: 4.4 }
      },
      playTrends: {
        daily: [450, 520, 480, 580, 550, 620, 480],
        hourly: Array.from({ length: 24 }, (_, i) => Math.floor(Math.random() * 100) + 20)
      }
    };

    res.json({
      success: true,
      data: {
        analytics,
        period,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Get song analytics error:', error);
    res.status(500).json({
      success: false,
      message: '获取歌曲分析数据失败'
    });
  }
});

/**
 * 获取问卷分析数据
 * GET /api/admin/analytics/surveys
 */
router.get('/analytics/surveys', (req, res) => {
  try {
    const { period = '7d' } = req.query;

    // 模拟问卷分析数据
    const analytics = {
      completionStats: {
        totalSurveys: 2340,
        completionRate: 85.5,
        avgCompletionTime: 120, // 秒
        avgRating: 4.2
      },
      ratingDistribution: {
        '5': 45,
        '4': 35,
        '3': 15,
        '2': 4,
        '1': 1
      },
      questionAnalysis: [
        {
          questionId: 1,
          title: '喜欢这个曲子吗？',
          responses: { 'A': 60, 'B': 30, 'C': 10 }
        },
        {
          questionId: 2,
          title: '音响设备评价',
          responses: { '音质很好': 70, '音量适中': 80, '音效清晰': 75, '有杂音': 5 }
        }
      ],
      trends: {
        daily: [45, 52, 48, 58, 55, 62, 45],
        satisfaction: [4.1, 4.2, 4.0, 4.3, 4.2, 4.4, 4.2]
      }
    };

    res.json({
      success: true,
      data: {
        analytics,
        period,
        generatedAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Get survey analytics error:', error);
    res.status(500).json({
      success: false,
      message: '获取问卷分析数据失败'
    });
  }
});

/**
 * 系统配置管理
 * GET /api/admin/config
 */
router.get('/config', (req, res) => {
  try {
    const config = {
      system: {
        siteName: '壹人音',
        version: '1.0.0',
        environment: process.env.NODE_ENV || 'development',
        uptime: process.uptime()
      },
      features: {
        userRegistration: true,
        surveySystem: true,
        recommendationEngine: true,
        analytics: true
      },
      limits: {
        maxFileSize: '10MB',
        maxUsersPerDay: 1000,
        maxSurveysPerUser: 10
      }
    };

    res.json({
      success: true,
      data: { config }
    });

  } catch (error) {
    console.error('Get system config error:', error);
    res.status(500).json({
      success: false,
      message: '获取系统配置失败'
    });
  }
});

module.exports = router;
