/**
 * 歌曲管理路由
 * 功能：歌曲列表、搜索、播放统计、歌曲详情
 */

const express = require('express');
const router = express.Router();

// 模拟歌曲数据库
let songs = [
  {
    id: 1,
    title: 'Eye of Horus',
    artist: 'Desert Mystic',
    album: 'Ancient Winds',
    duration: 240,
    coverUrl: '/uploads/covers/eye-of-horus.jpg',
    audioUrl: '/uploads/audio/eye-of-horus.mp3',
    genre: 'ambient',
    mood: 'mysterious',
    tags: ['沙漠', '神秘', '古埃及', '冥想'],
    playCount: 1250,
    likeCount: 89,
    status: 1,
    createdAt: '2024-01-15T10:30:00Z'
  },
  {
    id: 2,
    title: 'Neon Dreams',
    artist: 'Cyber Wave',
    album: 'Future Sounds',
    duration: 180,
    coverUrl: '/uploads/covers/neon-dreams.jpg',
    audioUrl: '/uploads/audio/neon-dreams.mp3',
    genre: 'electronic',
    mood: 'energetic',
    tags: ['科技', '未来', '电子', '活力'],
    playCount: 2100,
    likeCount: 156,
    status: 1,
    createdAt: '2024-01-20T14:15:00Z'
  },
  {
    id: 3,
    title: 'Peaceful Morning',
    artist: 'Nature Harmony',
    album: 'Calm Collection',
    duration: 300,
    coverUrl: '/uploads/covers/peaceful-morning.jpg',
    audioUrl: '/uploads/audio/peaceful-morning.mp3',
    genre: 'ambient',
    mood: 'calm',
    tags: ['自然', '平静', '早晨', '放松'],
    playCount: 890,
    likeCount: 67,
    status: 1,
    createdAt: '2024-01-25T08:45:00Z'
  },
  {
    id: 4,
    title: 'Urban Pulse',
    artist: 'City Beats',
    album: 'Metropolitan',
    duration: 210,
    coverUrl: '/uploads/covers/urban-pulse.jpg',
    audioUrl: '/uploads/audio/urban-pulse.mp3',
    genre: 'electronic',
    mood: 'energetic',
    tags: ['城市', '节拍', '现代', '动感'],
    playCount: 1680,
    likeCount: 124,
    status: 1,
    createdAt: '2024-02-01T16:20:00Z'
  },
  {
    id: 5,
    title: 'Moonlight Serenade',
    artist: 'Luna Ensemble',
    album: 'Night Whispers',
    duration: 270,
    coverUrl: '/uploads/covers/moonlight-serenade.jpg',
    audioUrl: '/uploads/audio/moonlight-serenade.mp3',
    genre: 'ambient',
    mood: 'romantic',
    tags: ['月光', '浪漫', '夜晚', '温柔'],
    playCount: 1420,
    likeCount: 98,
    status: 1,
    createdAt: '2024-02-05T20:10:00Z'
  }
];

/**
 * 获取歌曲列表
 * GET /api/songs
 */
router.get('/', (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      genre, 
      mood, 
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    let filteredSongs = songs.filter(song => song.status === 1);

    // 按类型筛选
    if (genre) {
      filteredSongs = filteredSongs.filter(song => song.genre === genre);
    }

    // 按情绪筛选
    if (mood) {
      filteredSongs = filteredSongs.filter(song => song.mood === mood);
    }

    // 搜索功能
    if (search) {
      const searchLower = search.toLowerCase();
      filteredSongs = filteredSongs.filter(song => 
        song.title.toLowerCase().includes(searchLower) ||
        song.artist.toLowerCase().includes(searchLower) ||
        song.album.toLowerCase().includes(searchLower) ||
        song.tags.some(tag => tag.includes(search))
      );
    }

    // 排序
    filteredSongs.sort((a, b) => {
      const aValue = a[sortBy];
      const bValue = b[sortBy];
      
      if (sortOrder === 'desc') {
        return bValue > aValue ? 1 : -1;
      } else {
        return aValue > bValue ? 1 : -1;
      }
    });

    // 分页
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedSongs = filteredSongs.slice(startIndex, endIndex);

    res.json({
      success: true,
      data: {
        songs: paginatedSongs,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: filteredSongs.length,
          totalPages: Math.ceil(filteredSongs.length / limit)
        }
      }
    });

  } catch (error) {
    console.error('Get songs error:', error);
    res.status(500).json({
      success: false,
      message: '获取歌曲列表失败'
    });
  }
});

/**
 * 获取歌曲详情
 * GET /api/songs/:id
 */
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const song = songs.find(s => s.id === parseInt(id) && s.status === 1);

    if (!song) {
      return res.status(404).json({
        success: false,
        message: '歌曲不存在'
      });
    }

    res.json({
      success: true,
      data: { song }
    });

  } catch (error) {
    console.error('Get song detail error:', error);
    res.status(500).json({
      success: false,
      message: '获取歌曲详情失败'
    });
  }
});

/**
 * 记录播放次数
 * POST /api/songs/:id/play
 */
router.post('/:id/play', (req, res) => {
  try {
    const { id } = req.params;
    const { duration = 0 } = req.body; // 播放时长（秒）

    const songIndex = songs.findIndex(s => s.id === parseInt(id));
    
    if (songIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '歌曲不存在'
      });
    }

    // 更新播放次数（只有播放超过30秒才算一次播放）
    if (duration >= 30) {
      songs[songIndex].playCount += 1;
    }

    res.json({
      success: true,
      message: '播放记录已更新',
      data: {
        songId: parseInt(id),
        playCount: songs[songIndex].playCount,
        duration
      }
    });

  } catch (error) {
    console.error('Record play error:', error);
    res.status(500).json({
      success: false,
      message: '记录播放失败'
    });
  }
});

/**
 * 点赞/取消点赞歌曲
 * POST /api/songs/:id/like
 */
router.post('/:id/like', (req, res) => {
  try {
    const { id } = req.params;
    const { action = 'like' } = req.body; // 'like' 或 'unlike'

    const songIndex = songs.findIndex(s => s.id === parseInt(id));
    
    if (songIndex === -1) {
      return res.status(404).json({
        success: false,
        message: '歌曲不存在'
      });
    }

    // 更新点赞数
    if (action === 'like') {
      songs[songIndex].likeCount += 1;
    } else if (action === 'unlike' && songs[songIndex].likeCount > 0) {
      songs[songIndex].likeCount -= 1;
    }

    res.json({
      success: true,
      message: action === 'like' ? '点赞成功' : '取消点赞成功',
      data: {
        songId: parseInt(id),
        likeCount: songs[songIndex].likeCount,
        action
      }
    });

  } catch (error) {
    console.error('Like song error:', error);
    res.status(500).json({
      success: false,
      message: '操作失败'
    });
  }
});

/**
 * 获取热门歌曲
 * GET /api/songs/popular
 */
router.get('/popular', (req, res) => {
  try {
    const { limit = 10 } = req.query;

    // 按播放次数排序获取热门歌曲
    const popularSongs = songs
      .filter(song => song.status === 1)
      .sort((a, b) => b.playCount - a.playCount)
      .slice(0, parseInt(limit));

    res.json({
      success: true,
      data: { songs: popularSongs }
    });

  } catch (error) {
    console.error('Get popular songs error:', error);
    res.status(500).json({
      success: false,
      message: '获取热门歌曲失败'
    });
  }
});

module.exports = router;
