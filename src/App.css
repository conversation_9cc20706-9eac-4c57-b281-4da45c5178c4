/**
 * 壹人音移动音乐应用 - 全局样式
 * 移动端优先设计，现代化UI风格
 */

/* 全局重置和基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow-x: hidden;
}

#root {
  height: 100%;
}

.app {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 404页面 */
.not-found {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
  color: white;
  padding: 20px;
}

.not-found h2 {
  font-size: 24px;
  margin-bottom: 12px;
}

.not-found p {
  font-size: 16px;
  opacity: 0.8;
}

/* 页面容器 */
.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 20px 20px 0 0;
  margin-top: 60px;
  overflow: hidden;
}

.page-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
  position: sticky;
  top: 0;
  z-index: 100;
}

.page-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 顶部状态栏 */
.status-bar {
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

/* 底部导航栏 */
.bottom-nav {
  height: 80px;
  background: white;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0;
  position: sticky;
  bottom: 0;
  z-index: 100;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 60px;
}

.nav-item:hover {
  background: #f5f5f5;
}

.nav-item.active {
  background: #e6f7ff;
  color: #1890ff;
}

.nav-item .icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.nav-item .label {
  font-size: 12px;
  font-weight: 500;
}

/* 卡片样式 */
.card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

/* 歌曲卡片 */
.song-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: white;
  border-radius: 12px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
}

.song-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-1px);
}

.song-card.playing {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid #1890ff;
}

.song-cover {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  background: #f5f5f5;
  margin-right: 16px;
  overflow: hidden;
}

.song-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.song-info {
  flex: 1;
}

.song-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
  line-height: 1.4;
}

.song-artist {
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.song-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.song-tag {
  background: #f0f0f0;
  color: #666;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.song-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.play-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #1890ff;
  color: white;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.play-button:hover {
  background: #40a9ff;
  transform: scale(1.1);
}

.play-button.playing {
  background: #52c41a;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px;
  }
  
  .card {
    padding: 16px;
    margin-bottom: 12px;
  }
  
  .song-card {
    padding: 12px;
  }
  
  .song-cover {
    width: 50px;
    height: 50px;
    margin-right: 12px;
  }
  
  .song-title {
    font-size: 15px;
  }
  
  .song-artist {
    font-size: 13px;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 动画效果 */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .page-container {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
  
  .bottom-nav {
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }
}
