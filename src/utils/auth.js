/**
 * 认证工具函数
 * 功能：Token管理、用户状态管理
 */

const TOKEN_KEY = 'yiren_music_token';
const USER_KEY = 'yiren_music_user';

/**
 * 保存认证token
 * @param {string} token - JWT token
 */
export const saveToken = (token) => {
  try {
    localStorage.setItem(TOKEN_KEY, token);
  } catch (error) {
    console.error('保存token失败:', error);
  }
};

/**
 * 获取认证token
 * @returns {string|null} JWT token
 */
export const getToken = () => {
  try {
    return localStorage.getItem(TOKEN_KEY);
  } catch (error) {
    console.error('获取token失败:', error);
    return null;
  }
};

/**
 * 移除认证token
 */
export const removeToken = () => {
  try {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
  } catch (error) {
    console.error('移除token失败:', error);
  }
};

/**
 * 保存用户信息
 * @param {Object} user - 用户信息对象
 */
export const saveUser = (user) => {
  try {
    localStorage.setItem(USER_KEY, JSON.stringify(user));
  } catch (error) {
    console.error('保存用户信息失败:', error);
  }
};

/**
 * 获取用户信息
 * @returns {Object|null} 用户信息对象
 */
export const getUser = () => {
  try {
    const userStr = localStorage.getItem(USER_KEY);
    return userStr ? JSON.parse(userStr) : null;
  } catch (error) {
    console.error('获取用户信息失败:', error);
    return null;
  }
};

/**
 * 检查用户是否已登录
 * @returns {boolean} 是否已登录
 */
export const isAuthenticated = () => {
  const token = getToken();
  const user = getUser();
  return !!(token && user);
};

/**
 * 解析JWT token获取用户信息
 * @param {string} token - JWT token
 * @returns {Object|null} 解析后的用户信息
 */
export const parseToken = (token) => {
  try {
    if (!token) return null;
    
    const parts = token.split('.');
    if (parts.length !== 3) return null;
    
    const payload = parts[1];
    const decoded = JSON.parse(atob(payload));
    
    // 检查token是否过期
    if (decoded.exp && decoded.exp * 1000 < Date.now()) {
      removeToken();
      return null;
    }
    
    return decoded;
  } catch (error) {
    console.error('解析token失败:', error);
    return null;
  }
};

/**
 * 检查token是否即将过期（30分钟内）
 * @param {string} token - JWT token
 * @returns {boolean} 是否即将过期
 */
export const isTokenExpiringSoon = (token) => {
  try {
    const decoded = parseToken(token);
    if (!decoded || !decoded.exp) return false;
    
    const expirationTime = decoded.exp * 1000;
    const currentTime = Date.now();
    const thirtyMinutes = 30 * 60 * 1000;
    
    return (expirationTime - currentTime) < thirtyMinutes;
  } catch (error) {
    console.error('检查token过期时间失败:', error);
    return false;
  }
};

/**
 * 格式化手机号
 * @param {string} phone - 原始手机号
 * @returns {string} 格式化后的手机号
 */
export const formatPhone = (phone) => {
  if (!phone) return '';
  
  // 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, '');
  
  // 格式化为 xxx-xxxx-xxxx
  if (cleaned.length === 11) {
    return `${cleaned.slice(0, 3)}-${cleaned.slice(3, 7)}-${cleaned.slice(7)}`;
  }
  
  return cleaned;
};

/**
 * 验证手机号格式
 * @param {string} phone - 手机号
 * @returns {boolean} 是否为有效手机号
 */
export const validatePhone = (phone) => {
  if (!phone) return false;
  
  // 移除所有非数字字符
  const cleaned = phone.replace(/\D/g, '');
  
  // 中国大陆手机号正则表达式
  const phoneRegex = /^1[3-9]\d{9}$/;
  
  return phoneRegex.test(cleaned);
};

/**
 * 生成随机字符串（用于临时ID等）
 * @param {number} length - 字符串长度
 * @returns {string} 随机字符串
 */
export const generateRandomString = (length = 8) => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
};

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @returns {Function} 防抖后的函数
 */
export const debounce = (func, wait) => {
  let timeout;
  
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
};

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 时间限制（毫秒）
 * @returns {Function} 节流后的函数
 */
export const throttle = (func, limit) => {
  let inThrottle;
  
  return function executedFunction(...args) {
    if (!inThrottle) {
      func.apply(this, args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};
