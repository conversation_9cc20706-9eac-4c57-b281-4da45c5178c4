/**
 * API请求工具函数
 * 功能：统一的API请求处理、错误处理、拦截器
 */

import { getToken, removeToken } from './auth';

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3001/api';

/**
 * 通用API请求函数
 * @param {string} endpoint - API端点
 * @param {Object} options - 请求选项
 * @returns {Promise} API响应
 */
export const apiRequest = async (endpoint, options = {}) => {
  const url = `${API_BASE_URL}${endpoint}`;
  
  // 默认请求配置
  const defaultOptions = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
    },
  };

  // 合并配置
  const config = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  };

  // 添加认证token
  const token = getToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  try {
    console.log(`API请求: ${config.method} ${url}`);
    
    const response = await fetch(url, config);
    
    // 检查响应状态
    if (!response.ok) {
      // 如果是401错误，清除token并重定向到登录页
      if (response.status === 401) {
        removeToken();
        window.location.href = '/login';
        throw new Error('认证失败，请重新登录');
      }
      
      // 尝试解析错误响应
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorMessage;
      } catch (e) {
        // 如果无法解析JSON，使用默认错误信息
      }
      
      throw new Error(errorMessage);
    }

    // 解析JSON响应
    const data = await response.json();
    console.log(`API响应:`, data);
    
    return data;
    
  } catch (error) {
    console.error(`API请求失败 ${config.method} ${url}:`, error);
    
    // 网络错误处理
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error('网络连接失败，请检查网络设置');
    }
    
    throw error;
  }
};

/**
 * GET请求
 * @param {string} endpoint - API端点
 * @param {Object} params - 查询参数
 * @returns {Promise} API响应
 */
export const get = async (endpoint, params = {}) => {
  const queryString = new URLSearchParams(params).toString();
  const url = queryString ? `${endpoint}?${queryString}` : endpoint;
  
  return apiRequest(url, {
    method: 'GET',
  });
};

/**
 * POST请求
 * @param {string} endpoint - API端点
 * @param {Object} data - 请求数据
 * @returns {Promise} API响应
 */
export const post = async (endpoint, data = {}) => {
  return apiRequest(endpoint, {
    method: 'POST',
    body: JSON.stringify(data),
  });
};

/**
 * PUT请求
 * @param {string} endpoint - API端点
 * @param {Object} data - 请求数据
 * @returns {Promise} API响应
 */
export const put = async (endpoint, data = {}) => {
  return apiRequest(endpoint, {
    method: 'PUT',
    body: JSON.stringify(data),
  });
};

/**
 * DELETE请求
 * @param {string} endpoint - API端点
 * @returns {Promise} API响应
 */
export const del = async (endpoint) => {
  return apiRequest(endpoint, {
    method: 'DELETE',
  });
};

/**
 * 文件上传请求
 * @param {string} endpoint - API端点
 * @param {FormData} formData - 表单数据
 * @returns {Promise} API响应
 */
export const upload = async (endpoint, formData) => {
  const token = getToken();
  const headers = {};
  
  if (token) {
    headers.Authorization = `Bearer ${token}`;
  }
  
  return apiRequest(endpoint, {
    method: 'POST',
    headers,
    body: formData,
  });
};

// 具体的API函数

/**
 * 用户认证相关API
 */
export const authAPI = {
  // 发送验证码
  sendCode: (phone) => post('/auth/send-code', { phone }),
  
  // 手机号登录
  phoneLogin: (phone, verificationCode) => 
    post('/auth/phone-login', { phone, verificationCode }),
  
  // 验证token
  verifyToken: (token) => post('/auth/verify-token', { token }),
  
  // 退出登录
  logout: () => post('/auth/logout'),
};

/**
 * 用户相关API
 */
export const userAPI = {
  // 获取用户信息
  getProfile: (userId) => get(`/users/${userId}`),
  
  // 更新用户信息
  updateProfile: (userId, data) => put(`/users/${userId}`, data),
  
  // 更新用户偏好
  updatePreferences: (userId, preferences) => 
    put(`/users/${userId}/preferences`, preferences),
  
  // 获取用户统计
  getStats: (userId) => get(`/users/${userId}/stats`),
};

/**
 * 歌曲相关API
 */
export const songAPI = {
  // 获取歌曲列表
  getSongs: (params) => get('/songs', params),
  
  // 获取歌曲详情
  getSongDetail: (songId) => get(`/songs/${songId}`),
  
  // 记录播放
  recordPlay: (songId, duration) => post(`/songs/${songId}/play`, { duration }),
  
  // 点赞/取消点赞
  likeSong: (songId, action) => post(`/songs/${songId}/like`, { action }),
  
  // 获取热门歌曲
  getPopularSongs: (limit) => get('/songs/popular', { limit }),
};

/**
 * 推荐相关API
 */
export const recommendationAPI = {
  // 获取个性化推荐
  getPersonalized: (userId, userPreferences, limit) => 
    post('/recommendations/personalized', { userId, userPreferences, limit }),
  
  // 获取每日推荐
  getDaily: (userId, limit) => get('/recommendations/daily', { userId, limit }),
  
  // 获取情绪推荐
  getMoodRecommendations: (mood, userId, limit) => 
    get(`/recommendations/mood/${mood}`, { userId, limit }),
  
  // 获取推荐历史
  getHistory: (userId, page, limit) => 
    get('/recommendations/history', { userId, page, limit }),
};

/**
 * 问卷相关API
 */
export const surveyAPI = {
  // 获取问卷模板
  getTemplates: () => get('/surveys/templates'),
  
  // 获取特定问卷模板
  getTemplate: (templateId) => get(`/surveys/templates/${templateId}`),
  
  // 提交问卷回答
  submitResponse: (data) => post('/surveys/responses', data),
  
  // 获取用户问卷历史
  getUserResponses: (userId, page, limit) => 
    get(`/surveys/responses/user/${userId}`, { page, limit }),
  
  // 获取歌曲问卷统计
  getSongStats: (songId) => get(`/surveys/stats/song/${songId}`),
  
  // 获取问卷概览统计
  getOverviewStats: (startDate, endDate) => 
    get('/surveys/stats/overview', { startDate, endDate }),
};

/**
 * 错误处理工具
 */
export const handleAPIError = (error, defaultMessage = '操作失败') => {
  console.error('API错误:', error);
  
  if (error.message) {
    return error.message;
  }
  
  return defaultMessage;
};
