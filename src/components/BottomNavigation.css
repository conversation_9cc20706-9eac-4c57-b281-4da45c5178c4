/**
 * 底部导航样式
 * 现代化移动端导航设计
 */

.bottom-navigation {
  height: 80px;
  background: white;
  border-top: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 8px 0;
  position: sticky;
  bottom: 0;
  z-index: 100;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 12px;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 60px;
  position: relative;
}

.nav-item:hover {
  background: #f5f5f5;
  transform: translateY(-2px);
}

.nav-item.active {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  color: #1890ff;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  top: -1px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: #1890ff;
  border-radius: 0 0 2px 2px;
}

.nav-icon {
  font-size: 20px;
  margin-bottom: 4px;
  transition: all 0.3s ease;
}

.nav-item.active .nav-icon {
  transform: scale(1.1);
}

.nav-label {
  font-size: 12px;
  font-weight: 500;
  line-height: 1;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .bottom-navigation {
    height: 70px;
    padding: 6px 0;
  }
  
  .nav-item {
    padding: 6px 8px;
    min-width: 50px;
  }
  
  .nav-icon {
    font-size: 18px;
    margin-bottom: 3px;
  }
  
  .nav-label {
    font-size: 11px;
  }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .bottom-navigation {
    padding-bottom: max(8px, env(safe-area-inset-bottom));
  }
}

/* 动画效果 */
@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-4px);
  }
  80% {
    transform: translateY(-2px);
  }
}

.nav-item:active {
  animation: bounce 0.6s ease;
}
