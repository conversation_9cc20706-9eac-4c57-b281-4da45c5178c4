/**
 * 壹人音移动音乐应用 - 主应用组件
 * 功能：路由管理、全局状态、主题配置
 */

import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider, App as AntdApp } from 'antd';
import zhCN from 'antd/locale/zh_CN';
import './App.css';

// 导入页面组件
import LoginPage from './pages/LoginPage';
import HomePage from './pages/HomePage';
import RecommendationPage from './pages/RecommendationPage';
import SurveyPage from './pages/SurveyPage';
import ProfilePage from './pages/ProfilePage';
import PlayerPage from './pages/PlayerPage';

// 导入工具函数
import { getToken, removeToken } from './utils/auth';
import { apiRequest } from './utils/api';

// 主题配置
const theme = {
  token: {
    colorPrimary: '#1890ff',
    colorSuccess: '#52c41a',
    colorWarning: '#faad14',
    colorError: '#ff4d4f',
    colorInfo: '#1890ff',
    borderRadius: 8,
    fontSize: 14,
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
  },
  components: {
    Button: {
      borderRadius: 20,
      controlHeight: 44,
    },
    Input: {
      borderRadius: 12,
      controlHeight: 44,
    },
    Card: {
      borderRadius: 16,
    },
  },
};

function App() {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [currentSong, setCurrentSong] = useState(null);
  const [isPlaying, setIsPlaying] = useState(false);

  // 检查用户登录状态
  useEffect(() => {
    const checkAuth = async () => {
      const token = getToken();
      if (token) {
        try {
          const response = await apiRequest('/auth/verify-token', {
            method: 'POST',
            body: JSON.stringify({ token }),
          });
          
          if (response.success) {
            setUser(response.data.user);
          } else {
            removeToken();
          }
        } catch (error) {
          console.error('验证token失败:', error);
          removeToken();
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, []);

  // 登录处理
  const handleLogin = (userData) => {
    setUser(userData);
  };

  // 登出处理
  const handleLogout = () => {
    removeToken();
    setUser(null);
    setCurrentSong(null);
    setIsPlaying(false);
  };

  // 播放歌曲
  const handlePlaySong = (song) => {
    setCurrentSong(song);
    setIsPlaying(true);
  };

  // 暂停/继续播放
  const handleTogglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  // 停止播放
  const handleStopPlay = () => {
    setCurrentSong(null);
    setIsPlaying(false);
  };

  // 受保护的路由组件
  const ProtectedRoute = ({ children }) => {
    if (loading) {
      return (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加载中...</p>
        </div>
      );
    }
    
    return user ? children : <Navigate to="/login" replace />;
  };

  // 公共路由组件（已登录用户重定向到首页）
  const PublicRoute = ({ children }) => {
    if (loading) {
      return (
        <div className="loading-container">
          <div className="loading-spinner"></div>
          <p>加载中...</p>
        </div>
      );
    }
    
    return user ? <Navigate to="/home" replace /> : children;
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>正在启动壹人音...</p>
      </div>
    );
  }

  return (
    <ConfigProvider theme={theme} locale={zhCN}>
      <AntdApp>
        <Router>
          <div className="app">
            <Routes>
              {/* 公共路由 */}
              <Route 
                path="/login" 
                element={
                  <PublicRoute>
                    <LoginPage onLogin={handleLogin} />
                  </PublicRoute>
                } 
              />
              
              {/* 受保护的路由 */}
              <Route 
                path="/home" 
                element={
                  <ProtectedRoute>
                    <HomePage 
                      user={user}
                      onPlaySong={handlePlaySong}
                      currentSong={currentSong}
                      isPlaying={isPlaying}
                    />
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/recommendations" 
                element={
                  <ProtectedRoute>
                    <RecommendationPage 
                      user={user}
                      onPlaySong={handlePlaySong}
                      currentSong={currentSong}
                      isPlaying={isPlaying}
                    />
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/survey/:songId" 
                element={
                  <ProtectedRoute>
                    <SurveyPage 
                      user={user}
                      currentSong={currentSong}
                    />
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/profile" 
                element={
                  <ProtectedRoute>
                    <ProfilePage 
                      user={user}
                      onLogout={handleLogout}
                    />
                  </ProtectedRoute>
                } 
              />
              
              <Route 
                path="/player" 
                element={
                  <ProtectedRoute>
                    <PlayerPage 
                      currentSong={currentSong}
                      isPlaying={isPlaying}
                      onTogglePlay={handleTogglePlay}
                      onStop={handleStopPlay}
                    />
                  </ProtectedRoute>
                } 
              />
              
              {/* 默认路由 */}
              <Route 
                path="/" 
                element={<Navigate to={user ? "/home" : "/login"} replace />} 
              />
              
              {/* 404页面 */}
              <Route 
                path="*" 
                element={
                  <div className="not-found">
                    <h2>页面不存在</h2>
                    <p>您访问的页面不存在，请检查URL是否正确。</p>
                  </div>
                } 
              />
            </Routes>
          </div>
        </Router>
      </AntdApp>
    </ConfigProvider>
  );
}

export default App;
