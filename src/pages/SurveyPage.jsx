/**
 * 问卷页面组件
 * 功能：音乐评价问卷、动态问题、评分提交
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Radio, Checkbox, Rate, Slider, message, Progress, Spin } from 'antd';
import { 
  ArrowLeftOutlined,
  CheckOutlined,
  SoundOutlined,
  HeartOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { surveyAPI, songAPI } from '../utils/api';
import './SurveyPage.css';

const { Title, Text, Paragraph } = Typography;

const SurveyPage = ({ user, currentSong }) => {
  const { songId } = useParams();
  const navigate = useNavigate();
  
  const [song, setSong] = useState(null);
  const [surveyTemplate, setSurveyTemplate] = useState(null);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [answers, setAnswers] = useState({});
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  // 加载歌曲信息和问卷模板
  useEffect(() => {
    loadSongAndSurvey();
  }, [songId]);

  const loadSongAndSurvey = async () => {
    try {
      setLoading(true);
      
      // 并行加载歌曲信息和问卷模板
      const [songResponse, surveyResponse] = await Promise.all([
        songAPI.getSongDetail(songId),
        surveyAPI.getTemplate('music_evaluation')
      ]);
      
      if (songResponse.success) {
        setSong(songResponse.data.song);
      }
      
      if (surveyResponse.success) {
        setSurveyTemplate(surveyResponse.data.template);
      }
      
    } catch (error) {
      console.error('加载失败:', error);
      message.error('加载失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理答案变化
  const handleAnswerChange = (questionId, value) => {
    setAnswers(prev => ({
      ...prev,
      [questionId]: value
    }));
  };

  // 下一题
  const handleNext = () => {
    if (currentQuestionIndex < surveyTemplate.questions.length - 1) {
      setCurrentQuestionIndex(currentQuestionIndex + 1);
    }
  };

  // 上一题
  const handlePrevious = () => {
    if (currentQuestionIndex > 0) {
      setCurrentQuestionIndex(currentQuestionIndex - 1);
    }
  };

  // 提交问卷
  const handleSubmit = async () => {
    try {
      setSubmitting(true);
      
      const surveyData = {
        userId: user.id,
        songId: parseInt(songId),
        templateId: surveyTemplate.id,
        answers: Object.entries(answers).map(([questionId, answer]) => ({
          questionId: parseInt(questionId),
          answer: answer
        })),
        completedAt: new Date().toISOString()
      };
      
      const response = await surveyAPI.submitResponse(surveyData);
      
      if (response.success) {
        message.success('感谢您的评价！');
        navigate('/home');
      } else {
        message.error(response.message || '提交失败');
      }
      
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 渲染问题组件
  const renderQuestion = (question) => {
    const answer = answers[question.id];
    
    switch (question.type) {
      case 'single_choice':
        return (
          <Radio.Group
            value={answer}
            onChange={(e) => handleAnswerChange(question.id, e.target.value)}
            className="question-radio-group"
          >
            {question.options.map(option => (
              <Radio key={option.value} value={option.value} className="question-radio">
                {option.label}
              </Radio>
            ))}
          </Radio.Group>
        );
        
      case 'multiple_choice':
        return (
          <Checkbox.Group
            value={answer || []}
            onChange={(values) => handleAnswerChange(question.id, values)}
            className="question-checkbox-group"
          >
            {question.options.map(option => (
              <Checkbox key={option.value} value={option.value} className="question-checkbox">
                {option.label}
              </Checkbox>
            ))}
          </Checkbox.Group>
        );
        
      case 'rating':
        return (
          <div className="question-rating">
            <Rate
              value={answer}
              onChange={(value) => handleAnswerChange(question.id, value)}
              character={<HeartOutlined />}
              className="rating-stars"
            />
            <Text className="rating-text">
              {answer ? `${answer} 星` : '请评分'}
            </Text>
          </div>
        );
        
      case 'scale':
        return (
          <div className="question-scale">
            <Slider
              min={question.min || 1}
              max={question.max || 10}
              value={answer || question.min || 1}
              onChange={(value) => handleAnswerChange(question.id, value)}
              marks={{
                [question.min || 1]: question.minLabel || '最低',
                [question.max || 10]: question.maxLabel || '最高'
              }}
              className="scale-slider"
            />
            <Text className="scale-value">当前值: {answer || question.min || 1}</Text>
          </div>
        );
        
      default:
        return null;
    }
  };

  // 检查当前问题是否已回答
  const isCurrentQuestionAnswered = () => {
    if (!surveyTemplate || !surveyTemplate.questions[currentQuestionIndex]) {
      return false;
    }
    
    const currentQuestion = surveyTemplate.questions[currentQuestionIndex];
    const answer = answers[currentQuestion.id];
    
    if (currentQuestion.type === 'multiple_choice') {
      return answer && answer.length > 0;
    }
    
    return answer !== undefined && answer !== null && answer !== '';
  };

  // 检查是否所有问题都已回答
  const areAllQuestionsAnswered = () => {
    if (!surveyTemplate) return false;
    
    return surveyTemplate.questions.every(question => {
      const answer = answers[question.id];
      if (question.type === 'multiple_choice') {
        return answer && answer.length > 0;
      }
      return answer !== undefined && answer !== null && answer !== '';
    });
  };

  if (loading) {
    return (
      <div className="survey-page">
        <div className="loading-container">
          <Spin size="large" />
          <Text>加载问卷中...</Text>
        </div>
      </div>
    );
  }

  if (!song || !surveyTemplate) {
    return (
      <div className="survey-page">
        <div className="error-container">
          <Text>加载失败，请重试</Text>
          <Button onClick={() => navigate('/home')}>返回首页</Button>
        </div>
      </div>
    );
  }

  const currentQuestion = surveyTemplate.questions[currentQuestionIndex];
  const progress = ((currentQuestionIndex + 1) / surveyTemplate.questions.length) * 100;

  return (
    <div className="survey-page">
      {/* 顶部状态栏 */}
      <div className="status-bar">
        <div className="status-left">
          <Button 
            type="text" 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/home')}
            className="back-button"
          />
          <span>音乐评价</span>
        </div>
        <div className="status-right">
          <SoundOutlined />
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-container">
        <div className="page-content">
          {/* 歌曲信息 */}
          <Card className="song-info-card">
            <div className="song-info-content">
              <div className="song-cover">
                <img 
                  src={song.coverUrl || '/placeholder-cover.jpg'} 
                  alt={song.title}
                  onError={(e) => {
                    e.target.src = '/placeholder-cover.jpg';
                  }}
                />
              </div>
              <div className="song-details">
                <Title level={4} className="song-title">{song.title}</Title>
                <Text className="song-artist">{song.artist}</Text>
                <Text className="song-genre" type="secondary">{song.genre}</Text>
              </div>
            </div>
          </Card>

          {/* 进度条 */}
          <div className="progress-section">
            <Progress 
              percent={progress} 
              showInfo={false}
              strokeColor="#1890ff"
              className="survey-progress"
            />
            <Text className="progress-text">
              {currentQuestionIndex + 1} / {surveyTemplate.questions.length}
            </Text>
          </div>

          {/* 问题卡片 */}
          <Card className="question-card">
            <div className="question-content">
              <Title level={4} className="question-title">
                {currentQuestion.title}
              </Title>
              
              {currentQuestion.description && (
                <Paragraph className="question-description">
                  {currentQuestion.description}
                </Paragraph>
              )}
              
              <div className="question-input">
                {renderQuestion(currentQuestion)}
              </div>
            </div>
          </Card>

          {/* 操作按钮 */}
          <div className="action-buttons">
            <Button
              onClick={handlePrevious}
              disabled={currentQuestionIndex === 0}
              className="prev-button"
            >
              上一题
            </Button>
            
            {currentQuestionIndex === surveyTemplate.questions.length - 1 ? (
              <Button
                type="primary"
                onClick={handleSubmit}
                disabled={!areAllQuestionsAnswered()}
                loading={submitting}
                icon={<CheckOutlined />}
                className="submit-button"
              >
                提交评价
              </Button>
            ) : (
              <Button
                type="primary"
                onClick={handleNext}
                disabled={!isCurrentQuestionAnswered()}
                className="next-button"
              >
                下一题
              </Button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SurveyPage;
