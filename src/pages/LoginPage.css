/**
 * 登录页面样式
 * 现代化移动端设计，渐变背景，动画效果
 */

.login-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

/* 背景动画波浪 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

.wave {
  position: absolute;
  width: 200%;
  height: 200%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: wave 20s infinite linear;
}

.wave1 {
  top: -50%;
  left: -50%;
  animation-duration: 25s;
}

.wave2 {
  top: -60%;
  left: -60%;
  animation-duration: 30s;
  animation-direction: reverse;
  background: rgba(255, 255, 255, 0.05);
}

.wave3 {
  top: -70%;
  left: -40%;
  animation-duration: 35s;
  background: rgba(255, 255, 255, 0.03);
}

@keyframes wave {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 顶部Logo区域 */
.login-header {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px 20px 20px;
  position: relative;
  z-index: 1;
}

.logo-container {
  text-align: center;
  animation: fadeInDown 1s ease-out;
}

.logo-icon {
  font-size: 64px;
  color: white;
  margin-bottom: 16px;
  display: block;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: pulse 2s infinite;
}

.app-title {
  color: white !important;
  margin-bottom: 8px !important;
  font-weight: 700;
  font-size: 36px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-subtitle {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  font-weight: 300;
}

/* 登录表单容器 */
.login-form-container {
  flex: 1;
  padding: 0 20px 40px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.login-card {
  width: 100%;
  max-width: 400px;
  border-radius: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
  border: none;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  animation: slideUp 0.8s ease-out;
}

.login-card .ant-card-body {
  padding: 32px;
}

/* 表单头部 */
.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header .ant-typography {
  margin-bottom: 8px;
}

.form-header h3 {
  color: #262626;
  font-weight: 600;
}

/* 表单样式 */
.login-card .ant-form-item-label > label {
  font-weight: 500;
  color: #595959;
}

.login-card .ant-input-affix-wrapper,
.login-card .ant-input {
  border-radius: 12px;
  border: 1px solid #e8e8e8;
  transition: all 0.3s ease;
}

.login-card .ant-input-affix-wrapper:hover,
.login-card .ant-input:hover {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
}

.login-card .ant-input-affix-wrapper-focused,
.login-card .ant-input:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.login-card .ant-input-prefix {
  color: #8c8c8c;
  margin-right: 8px;
}

/* 登录按钮 */
.login-button {
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
  margin-top: 16px;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.login-button:active {
  transform: translateY(0);
}

/* 验证码按钮 */
.login-card .ant-btn {
  border-radius: 12px;
  font-weight: 500;
}

.login-card .ant-btn:disabled {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #bfbfbf;
}

/* 登录提示 */
.login-tips {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

.tip-text {
  display: block;
  margin-bottom: 8px;
  font-size: 13px;
  line-height: 1.4;
}

.tip-text:last-child {
  margin-bottom: 0;
}

/* 底部 */
.login-footer {
  padding: 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.login-footer .ant-typography {
  color: rgba(255, 255, 255, 0.8);
  font-size: 12px;
}

/* 动画效果 */
@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-header {
    padding: 20px 20px 10px;
  }
  
  .logo-icon {
    font-size: 48px;
  }
  
  .app-title {
    font-size: 28px !important;
  }
  
  .app-subtitle {
    font-size: 14px;
  }
  
  .login-form-container {
    padding: 0 16px 20px;
  }
  
  .login-card .ant-card-body {
    padding: 24px;
  }
  
  .form-header {
    margin-bottom: 24px;
  }
}

@media (max-height: 700px) {
  .login-header {
    flex: 0.8;
  }
  
  .logo-icon {
    font-size: 48px;
    margin-bottom: 12px;
  }
  
  .app-title {
    font-size: 28px !important;
    margin-bottom: 4px !important;
  }
}

/* 错误状态样式 */
.login-card .ant-form-item-has-error .ant-input-affix-wrapper,
.login-card .ant-form-item-has-error .ant-input {
  border-color: #ff4d4f;
}

.login-card .ant-form-item-has-error .ant-input-affix-wrapper:hover,
.login-card .ant-form-item-has-error .ant-input:hover {
  border-color: #ff7875;
}

/* 加载状态 */
.login-card .ant-btn-loading {
  pointer-events: none;
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .login-page {
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}
