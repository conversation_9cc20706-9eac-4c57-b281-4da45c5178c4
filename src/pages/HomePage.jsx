/**
 * 首页组件
 * 功能：歌曲推荐展示、快速播放、导航菜单
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Tag, message, Spin, Empty } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  HeartOutlined,
  HeartFilled,
  SoundOutlined,
  UserOutlined,
  SearchOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { recommendationAPI, songAPI } from '../utils/api';
import BottomNavigation from '../components/BottomNavigation';
import './HomePage.css';

const { Title, Text } = Typography;

const HomePage = ({ user, onPlaySong, currentSong, isPlaying }) => {
  const navigate = useNavigate();
  const [recommendations, setRecommendations] = useState([]);
  const [popularSongs, setPopularSongs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [likedSongs, setLikedSongs] = useState(new Set());

  // 加载推荐数据
  useEffect(() => {
    loadRecommendations();
    loadPopularSongs();
  }, [user]);

  // 获取个性化推荐
  const loadRecommendations = async () => {
    try {
      const response = await recommendationAPI.getPersonalized(
        user.id, 
        user.preferences, 
        3
      );
      
      if (response.success) {
        setRecommendations(response.data.recommendations);
      }
    } catch (error) {
      console.error('获取推荐失败:', error);
      message.error('获取推荐失败');
    }
  };

  // 获取热门歌曲
  const loadPopularSongs = async () => {
    try {
      const response = await songAPI.getPopularSongs(5);
      
      if (response.success) {
        setPopularSongs(response.data.songs);
      }
    } catch (error) {
      console.error('获取热门歌曲失败:', error);
      message.error('获取热门歌曲失败');
    } finally {
      setLoading(false);
    }
  };

  // 播放歌曲
  const handlePlaySong = async (song) => {
    try {
      // 记录播放行为
      await songAPI.recordPlay(song.id, 0);
      onPlaySong(song);
    } catch (error) {
      console.error('播放失败:', error);
      message.error('播放失败');
    }
  };

  // 点赞歌曲
  const handleLikeSong = async (song, e) => {
    e.stopPropagation();
    
    try {
      const isLiked = likedSongs.has(song.id);
      const action = isLiked ? 'unlike' : 'like';
      
      await songAPI.likeSong(song.id, action);
      
      const newLikedSongs = new Set(likedSongs);
      if (isLiked) {
        newLikedSongs.delete(song.id);
        message.success('取消点赞');
      } else {
        newLikedSongs.add(song.id);
        message.success('点赞成功');
      }
      
      setLikedSongs(newLikedSongs);
    } catch (error) {
      console.error('点赞失败:', error);
      message.error('操作失败');
    }
  };

  // 跳转到问卷页面
  const handleGoToSurvey = (songId) => {
    navigate(`/survey/${songId}`);
  };

  // 格式化时长
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 歌曲卡片组件
  const SongCard = ({ song, showSurveyButton = false }) => {
    const isCurrentSong = currentSong && currentSong.id === song.id;
    const isLiked = likedSongs.has(song.id);
    
    return (
      <Card 
        className={`song-card ${isCurrentSong ? 'playing' : ''}`}
        onClick={() => handlePlaySong(song)}
        hoverable
      >
        <div className="song-content">
          <div className="song-cover">
            <img 
              src={song.coverUrl || '/placeholder-cover.jpg'} 
              alt={song.title}
              onError={(e) => {
                e.target.src = '/placeholder-cover.jpg';
              }}
            />
            <div className="play-overlay">
              {isCurrentSong && isPlaying ? (
                <PauseCircleOutlined className="play-icon" />
              ) : (
                <PlayCircleOutlined className="play-icon" />
              )}
            </div>
          </div>
          
          <div className="song-info">
            <Title level={5} className="song-title">{song.title}</Title>
            <Text className="song-artist">{song.artist}</Text>
            <div className="song-meta">
              <Text type="secondary">{formatDuration(song.duration)}</Text>
              <Text type="secondary">•</Text>
              <Text type="secondary">{song.playCount} 播放</Text>
            </div>
            <div className="song-tags">
              {song.tags && song.tags.slice(0, 2).map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
            </div>
          </div>
          
          <div className="song-actions">
            <Button
              type="text"
              icon={isLiked ? <HeartFilled /> : <HeartOutlined />}
              className={`like-button ${isLiked ? 'liked' : ''}`}
              onClick={(e) => handleLikeSong(song, e)}
            />
            {showSurveyButton && (
              <Button
                type="primary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleGoToSurvey(song.id);
                }}
              >
                评价
              </Button>
            )}
          </div>
        </div>
      </Card>
    );
  };

  if (loading) {
    return (
      <div className="home-page">
        <div className="loading-container">
          <Spin size="large" />
          <Text>加载中...</Text>
        </div>
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* 顶部状态栏 */}
      <div className="status-bar">
        <div className="status-left">
          <SoundOutlined />
          <span>壹人音</span>
        </div>
        <div className="status-right">
          <Space>
            <SearchOutlined />
            <SettingOutlined />
            <UserOutlined />
          </Space>
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-container">
        <div className="page-content">
          {/* 欢迎区域 */}
          <div className="welcome-section">
            <Title level={3}>你好，{user.nickname}</Title>
            <Text type="secondary">为你精选了这些音乐</Text>
          </div>

          {/* 个性化推荐 */}
          <div className="section">
            <div className="section-header">
              <Title level={4}>个性化推荐</Title>
              <Button 
                type="link" 
                onClick={() => navigate('/recommendations')}
              >
                查看更多
              </Button>
            </div>
            
            {recommendations.length > 0 ? (
              <div className="songs-list">
                {recommendations.map(song => (
                  <SongCard 
                    key={song.id} 
                    song={song} 
                    showSurveyButton={true}
                  />
                ))}
              </div>
            ) : (
              <Empty 
                description="暂无推荐内容"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>

          {/* 热门歌曲 */}
          <div className="section">
            <div className="section-header">
              <Title level={4}>热门歌曲</Title>
            </div>
            
            {popularSongs.length > 0 ? (
              <div className="songs-list">
                {popularSongs.map(song => (
                  <SongCard key={song.id} song={song} />
                ))}
              </div>
            ) : (
              <Empty 
                description="暂无热门歌曲"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )}
          </div>
        </div>
      </div>

      {/* 底部导航 */}
      <BottomNavigation currentPath="/home" />
    </div>
  );
};

export default HomePage;
