/**
 * 播放器页面组件
 * 功能：音乐播放控制、进度条、歌词显示、播放列表
 */

import React, { useState, useEffect, useRef } from 'react';
import { Card, Button, Slider, Typography, Progress, message } from 'antd';
import { 
  PlayCircleOutlined,
  PauseCircleOutlined,
  StepBackwardOutlined,
  StepForwardOutlined,
  SoundOutlined,
  HeartOutlined,
  HeartFilled,
  DownloadOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { songAPI } from '../utils/api';
import BottomNavigation from '../components/BottomNavigation';
import './PlayerPage.css';

const { Title, Text } = Typography;

const PlayerPage = ({ 
  currentSong, 
  isPlaying, 
  onPlayPause, 
  onNext, 
  onPrevious,
  currentTime = 0,
  duration = 0,
  onSeek
}) => {
  const navigate = useNavigate();
  const [volume, setVolume] = useState(80);
  const [isLiked, setIsLiked] = useState(false);
  const [lyrics, setLyrics] = useState([]);
  const [currentLyricIndex, setCurrentLyricIndex] = useState(-1);

  // 加载歌曲详情和歌词
  useEffect(() => {
    if (currentSong) {
      loadSongDetails();
      loadLyrics();
    }
  }, [currentSong]);

  // 更新当前歌词
  useEffect(() => {
    if (lyrics.length > 0) {
      const index = lyrics.findIndex((lyric, i) => {
        const nextLyric = lyrics[i + 1];
        return currentTime >= lyric.time && (!nextLyric || currentTime < nextLyric.time);
      });
      setCurrentLyricIndex(index);
    }
  }, [currentTime, lyrics]);

  const loadSongDetails = async () => {
    try {
      const response = await songAPI.getSongDetail(currentSong.id);
      if (response.success) {
        setIsLiked(response.data.song.isLiked || false);
      }
    } catch (error) {
      console.error('获取歌曲详情失败:', error);
    }
  };

  const loadLyrics = async () => {
    try {
      const response = await songAPI.getLyrics(currentSong.id);
      if (response.success) {
        setLyrics(response.data.lyrics || []);
      }
    } catch (error) {
      console.error('获取歌词失败:', error);
      // 设置默认歌词
      setLyrics([
        { time: 0, text: '暂无歌词' },
        { time: 10, text: '请欣赏音乐' }
      ]);
    }
  };

  // 点赞歌曲
  const handleLikeSong = async () => {
    try {
      const action = isLiked ? 'unlike' : 'like';
      await songAPI.likeSong(currentSong.id, action);
      
      setIsLiked(!isLiked);
      message.success(isLiked ? '取消点赞' : '点赞成功');
    } catch (error) {
      console.error('点赞失败:', error);
      message.error('操作失败');
    }
  };

  // 分享歌曲
  const handleShare = () => {
    if (navigator.share) {
      navigator.share({
        title: currentSong.title,
        text: `正在听 ${currentSong.artist} 的《${currentSong.title}》`,
        url: window.location.href
      });
    } else {
      // 复制到剪贴板
      navigator.clipboard.writeText(
        `正在听 ${currentSong.artist} 的《${currentSong.title}》 - 壹人音`
      );
      message.success('已复制到剪贴板');
    }
  };

  // 格式化时间
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.floor(seconds % 60);
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 进度百分比
  const progressPercent = duration > 0 ? (currentTime / duration) * 100 : 0;

  if (!currentSong) {
    return (
      <div className="player-page">
        <div className="no-song-container">
          <SoundOutlined className="no-song-icon" />
          <Title level={4}>暂无播放歌曲</Title>
          <Text type="secondary">请先选择一首歌曲</Text>
          <Button 
            type="primary" 
            onClick={() => navigate('/home')}
            className="go-home-button"
          >
            去首页选歌
          </Button>
        </div>
        <BottomNavigation currentPath="/player" />
      </div>
    );
  }

  return (
    <div className="player-page">
      {/* 背景 */}
      <div 
        className="player-background"
        style={{
          backgroundImage: `url(${currentSong.coverUrl || '/placeholder-cover.jpg'})`
        }}
      >
        <div className="background-overlay"></div>
      </div>

      {/* 页面内容 */}
      <div className="player-content">
        {/* 歌曲封面 */}
        <div className="song-cover-container">
          <div className={`song-cover ${isPlaying ? 'rotating' : ''}`}>
            <img 
              src={currentSong.coverUrl || '/placeholder-cover.jpg'} 
              alt={currentSong.title}
              onError={(e) => {
                e.target.src = '/placeholder-cover.jpg';
              }}
            />
          </div>
        </div>

        {/* 歌曲信息 */}
        <div className="song-info">
          <Title level={3} className="song-title">{currentSong.title}</Title>
          <Text className="song-artist">{currentSong.artist}</Text>
        </div>

        {/* 歌词显示 */}
        <div className="lyrics-container">
          {lyrics.length > 0 ? (
            <div className="lyrics-scroll">
              {lyrics.map((lyric, index) => (
                <div
                  key={index}
                  className={`lyric-line ${
                    index === currentLyricIndex ? 'active' : ''
                  } ${
                    index < currentLyricIndex ? 'passed' : ''
                  }`}
                >
                  {lyric.text}
                </div>
              ))}
            </div>
          ) : (
            <div className="no-lyrics">
              <Text type="secondary">暂无歌词</Text>
            </div>
          )}
        </div>

        {/* 进度控制 */}
        <div className="progress-container">
          <div className="progress-bar">
            <Slider
              value={progressPercent}
              onChange={(value) => onSeek && onSeek((value / 100) * duration)}
              tooltip={{ formatter: null }}
              className="progress-slider"
            />
          </div>
          <div className="time-display">
            <Text className="current-time">{formatTime(currentTime)}</Text>
            <Text className="total-time">{formatTime(duration)}</Text>
          </div>
        </div>

        {/* 播放控制 */}
        <div className="player-controls">
          <div className="main-controls">
            <Button
              type="text"
              icon={<StepBackwardOutlined />}
              onClick={onPrevious}
              className="control-button"
              size="large"
            />
            <Button
              type="text"
              icon={isPlaying ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
              onClick={onPlayPause}
              className="play-button"
              size="large"
            />
            <Button
              type="text"
              icon={<StepForwardOutlined />}
              onClick={onNext}
              className="control-button"
              size="large"
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="action-controls">
          <Button
            type="text"
            icon={isLiked ? <HeartFilled /> : <HeartOutlined />}
            onClick={handleLikeSong}
            className={`action-button ${isLiked ? 'liked' : ''}`}
          />
          <Button
            type="text"
            icon={<ShareAltOutlined />}
            onClick={handleShare}
            className="action-button"
          />
          <Button
            type="text"
            icon={<DownloadOutlined />}
            onClick={() => message.info('下载功能开发中')}
            className="action-button"
          />
        </div>

        {/* 音量控制 */}
        <div className="volume-container">
          <SoundOutlined className="volume-icon" />
          <Slider
            value={volume}
            onChange={setVolume}
            className="volume-slider"
            tooltip={{ formatter: (value) => `${value}%` }}
          />
        </div>
      </div>

      {/* 底部导航 */}
      <BottomNavigation currentPath="/player" />
    </div>
  );
};

export default PlayerPage;
