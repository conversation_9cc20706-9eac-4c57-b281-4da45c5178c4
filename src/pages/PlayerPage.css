/**
 * 播放器页面样式
 * 现代化移动端设计，沉浸式播放体验
 */

.player-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
}

/* 无歌曲状态 */
.no-song-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 16px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.no-song-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.8;
}

.no-song-container h4 {
  color: white !important;
  margin-bottom: 8px !important;
}

.go-home-button {
  margin-top: 20px;
  border-radius: 24px;
  height: 48px;
  padding: 0 24px;
  font-size: 16px;
  font-weight: 600;
}

/* 背景 */
.player-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-size: cover;
  background-position: center;
  filter: blur(20px);
  transform: scale(1.1);
}

.background-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    rgba(102, 126, 234, 0.8) 0%,
    rgba(118, 75, 162, 0.8) 100%
  );
}

/* 页面内容 */
.player-content {
  position: relative;
  z-index: 1;
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 40px 20px 20px;
  color: white;
}

/* 歌曲封面 */
.song-cover-container {
  display: flex;
  justify-content: center;
  margin-bottom: 32px;
}

.song-cover {
  width: 280px;
  height: 280px;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  position: relative;
  transition: transform 0.3s ease;
}

.song-cover.rotating {
  animation: rotate 20s linear infinite;
}

.song-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 歌曲信息 */
.song-info {
  text-align: center;
  margin-bottom: 32px;
}

.song-title {
  color: white !important;
  margin-bottom: 8px !important;
  font-size: 24px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.song-artist {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 歌词容器 */
.lyrics-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  min-height: 120px;
}

.lyrics-scroll {
  max-height: 120px;
  overflow-y: auto;
  text-align: center;
  padding: 0 20px;
}

.lyric-line {
  font-size: 16px;
  line-height: 1.8;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.6);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.lyric-line.active {
  color: white;
  font-size: 18px;
  font-weight: 600;
  transform: scale(1.05);
}

.lyric-line.passed {
  color: rgba(255, 255, 255, 0.4);
}

.no-lyrics {
  text-align: center;
}

/* 进度控制 */
.progress-container {
  margin-bottom: 32px;
}

.progress-bar {
  margin-bottom: 12px;
}

.progress-slider .ant-slider-rail {
  background: rgba(255, 255, 255, 0.3);
  height: 4px;
}

.progress-slider .ant-slider-track {
  background: white;
  height: 4px;
}

.progress-slider .ant-slider-handle {
  width: 16px;
  height: 16px;
  border: 3px solid white;
  margin-top: -6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.time-display {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

/* 播放控制 */
.player-controls {
  margin-bottom: 24px;
}

.main-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
}

.control-button {
  color: white !important;
  border: none !important;
  background: transparent !important;
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.control-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

.play-button {
  color: white !important;
  border: none !important;
  background: rgba(255, 255, 255, 0.2) !important;
  font-size: 48px;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.play-button:hover {
  background: rgba(255, 255, 255, 0.3) !important;
  transform: scale(1.05);
}

/* 操作按钮 */
.action-controls {
  display: flex;
  justify-content: center;
  gap: 40px;
  margin-bottom: 24px;
}

.action-button {
  color: rgba(255, 255, 255, 0.8) !important;
  border: none !important;
  background: transparent !important;
  font-size: 24px;
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.action-button:hover {
  color: white !important;
  background: rgba(255, 255, 255, 0.1) !important;
  transform: scale(1.1);
}

.action-button.liked {
  color: #ff4d4f !important;
}

/* 音量控制 */
.volume-container {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 20px;
}

.volume-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
}

.volume-slider {
  flex: 1;
}

.volume-slider .ant-slider-rail {
  background: rgba(255, 255, 255, 0.3);
  height: 3px;
}

.volume-slider .ant-slider-track {
  background: white;
  height: 3px;
}

.volume-slider .ant-slider-handle {
  width: 12px;
  height: 12px;
  border: 2px solid white;
  margin-top: -4.5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .player-content {
    padding: 30px 16px 16px;
  }

  .song-cover {
    width: 240px;
    height: 240px;
  }

  .song-title {
    font-size: 20px;
  }

  .song-artist {
    font-size: 14px;
  }

  .lyrics-container {
    min-height: 100px;
  }

  .lyric-line {
    font-size: 14px;
  }

  .lyric-line.active {
    font-size: 16px;
  }

  .main-controls {
    gap: 24px;
  }

  .control-button {
    font-size: 28px;
    width: 48px;
    height: 48px;
  }

  .play-button {
    font-size: 40px;
    width: 72px;
    height: 72px;
  }

  .action-controls {
    gap: 32px;
  }

  .action-button {
    font-size: 20px;
    width: 40px;
    height: 40px;
  }
}

@media (max-width: 480px) {
  .player-content {
    padding: 20px 12px 12px;
  }

  .song-cover {
    width: 200px;
    height: 200px;
  }

  .song-info {
    margin-bottom: 24px;
  }

  .song-title {
    font-size: 18px;
  }

  .song-artist {
    font-size: 13px;
  }

  .lyrics-container {
    min-height: 80px;
    margin-bottom: 24px;
  }

  .lyrics-scroll {
    padding: 0 12px;
  }

  .lyric-line {
    font-size: 13px;
    line-height: 1.6;
  }

  .lyric-line.active {
    font-size: 15px;
  }

  .progress-container {
    margin-bottom: 24px;
  }

  .main-controls {
    gap: 20px;
  }

  .control-button {
    font-size: 24px;
    width: 44px;
    height: 44px;
  }

  .play-button {
    font-size: 36px;
    width: 64px;
    height: 64px;
  }

  .action-controls {
    gap: 28px;
    margin-bottom: 20px;
  }

  .action-button {
    font-size: 18px;
    width: 36px;
    height: 36px;
  }

  .volume-container {
    margin-bottom: 16px;
  }
}

/* 动画效果 */
.player-content {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.lyrics-scroll::-webkit-scrollbar {
  width: 2px;
}

.lyrics-scroll::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.lyrics-scroll::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
}

.lyrics-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
  .player-content {
    padding-top: max(40px, env(safe-area-inset-top) + 20px);
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
}
