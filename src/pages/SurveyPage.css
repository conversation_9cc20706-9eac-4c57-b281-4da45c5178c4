/**
 * 问卷页面样式
 * 现代化移动端设计，卡片布局，交互友好
 */

.survey-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  gap: 16px;
  color: white;
}

/* 顶部状态栏 */
.status-bar {
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.back-button {
  color: white !important;
  border: none !important;
  background: transparent !important;
  padding: 4px !important;
}

.back-button:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* 页面容器 */
.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.page-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 歌曲信息卡片 */
.song-info-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.song-info-card .ant-card-body {
  padding: 20px;
}

.song-info-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.song-cover {
  width: 80px;
  height: 80px;
  border-radius: 12px;
  overflow: hidden;
  flex-shrink: 0;
  background: #f5f5f5;
}

.song-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.song-details {
  flex: 1;
  min-width: 0;
}

.song-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
}

.song-artist {
  display: block;
  font-size: 16px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.song-genre {
  font-size: 14px;
}

/* 进度条部分 */
.progress-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.survey-progress {
  margin: 0;
}

.survey-progress .ant-progress-bg {
  border-radius: 4px;
}

.progress-text {
  text-align: center;
  font-size: 14px;
  color: #8c8c8c;
  font-weight: 500;
}

/* 问题卡片 */
.question-card {
  flex: 1;
  border-radius: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  min-height: 300px;
}

.question-card .ant-card-body {
  padding: 24px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.question-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.question-title {
  margin: 0 0 16px 0;
  font-size: 20px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
}

.question-description {
  margin-bottom: 24px;
  color: #595959;
  font-size: 14px;
  line-height: 1.6;
}

.question-input {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* 单选题样式 */
.question-radio-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-radio {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  transition: all 0.3s ease;
  margin: 0;
}

.question-radio:hover {
  border-color: #1890ff;
  background: #f0f9ff;
}

.question-radio.ant-radio-wrapper-checked {
  border-color: #1890ff;
  background: #e6f7ff;
}

.question-radio .ant-radio {
  margin-right: 12px;
}

/* 多选题样式 */
.question-checkbox-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.question-checkbox {
  padding: 16px;
  border: 1px solid #e8e8e8;
  border-radius: 12px;
  transition: all 0.3s ease;
  margin: 0;
}

.question-checkbox:hover {
  border-color: #1890ff;
  background: #f0f9ff;
}

.question-checkbox.ant-checkbox-wrapper-checked {
  border-color: #1890ff;
  background: #e6f7ff;
}

.question-checkbox .ant-checkbox {
  margin-right: 12px;
}

/* 评分样式 */
.question-rating {
  text-align: center;
  padding: 20px 0;
}

.rating-stars {
  font-size: 32px;
  margin-bottom: 16px;
}

.rating-stars .ant-rate-star {
  margin-right: 8px;
}

.rating-text {
  font-size: 16px;
  color: #595959;
  font-weight: 500;
}

/* 滑块样式 */
.question-scale {
  padding: 20px 0;
}

.scale-slider {
  margin-bottom: 20px;
}

.scale-slider .ant-slider-rail {
  background: #f5f5f5;
  height: 6px;
}

.scale-slider .ant-slider-track {
  background: #1890ff;
  height: 6px;
}

.scale-slider .ant-slider-handle {
  width: 20px;
  height: 20px;
  border: 3px solid #1890ff;
  margin-top: -7px;
}

.scale-value {
  text-align: center;
  display: block;
  font-size: 16px;
  color: #595959;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: space-between;
  gap: 16px;
  padding-top: 20px;
}

.prev-button,
.next-button,
.submit-button {
  flex: 1;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
}

.prev-button {
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  color: #595959;
}

.prev-button:hover:not(:disabled) {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.next-button,
.submit-button {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.next-button:hover:not(:disabled),
.submit-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(24, 144, 255, 0.4);
}

.submit-button {
  background: linear-gradient(135deg, #52c41a 0%, #1890ff 100%);
  box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
}

.submit-button:hover:not(:disabled) {
  box-shadow: 0 6px 16px rgba(82, 196, 26, 0.4);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px;
    gap: 16px;
  }
  
  .song-info-card .ant-card-body {
    padding: 16px;
  }
  
  .song-info-content {
    gap: 12px;
  }
  
  .song-cover {
    width: 60px;
    height: 60px;
  }
  
  .song-title {
    font-size: 16px;
  }
  
  .song-artist {
    font-size: 14px;
  }
  
  .question-card .ant-card-body {
    padding: 20px;
  }
  
  .question-title {
    font-size: 18px;
  }
  
  .rating-stars {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .status-bar {
    padding: 0 16px;
  }
  
  .page-content {
    padding: 12px;
    gap: 12px;
  }
  
  .song-info-card .ant-card-body {
    padding: 12px;
  }
  
  .song-info-content {
    gap: 10px;
  }
  
  .song-cover {
    width: 50px;
    height: 50px;
  }
  
  .song-title {
    font-size: 15px;
  }
  
  .song-artist {
    font-size: 13px;
  }
  
  .question-card .ant-card-body {
    padding: 16px;
  }
  
  .question-title {
    font-size: 16px;
  }
  
  .question-radio,
  .question-checkbox {
    padding: 12px;
  }
  
  .rating-stars {
    font-size: 24px;
  }
  
  .action-buttons {
    gap: 12px;
  }
  
  .prev-button,
  .next-button,
  .submit-button {
    height: 44px;
    font-size: 14px;
  }
}

/* 动画效果 */
.question-card {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 禁用状态 */
.prev-button:disabled,
.next-button:disabled,
.submit-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}
