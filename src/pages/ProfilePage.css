/**
 * 个人资料页面样式
 * 现代化移动端设计，卡片布局，统计展示
 */

.profile-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 顶部状态栏 */
.status-bar {
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-right .ant-btn {
  color: white;
  border: none;
  background: transparent;
}

.status-right .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 页面容器 */
.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.page-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 用户信息卡片 */
.user-info-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.user-info-card .ant-card-body {
  padding: 24px;
}

.user-info-content {
  display: flex;
  align-items: center;
  gap: 20px;
}

.user-avatar {
  flex-shrink: 0;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
  line-height: 1.2;
}

.user-phone {
  display: block;
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 12px;
}

.user-badges {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.edit-button {
  flex-shrink: 0;
  border-radius: 20px;
  height: 36px;
  padding: 0 16px;
  font-size: 14px;
}

/* 偏好设置卡片 */
.preferences-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.preferences-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.preferences-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.preferences-card .ant-card-body {
  padding: 20px 24px;
}

.preference-section {
  margin-bottom: 20px;
}

.preference-section:last-child {
  margin-bottom: 0;
}

.preference-section .ant-typography {
  margin-bottom: 12px;
  font-size: 14px;
}

.preference-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preference-tag {
  margin: 0;
  border-radius: 12px;
  font-size: 12px;
  padding: 4px 8px;
  font-weight: 500;
}

/* 统计数据卡片 */
.stats-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.stats-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.stats-card .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
  color: #262626;
}

.stats-card .ant-card-body {
  padding: 20px 24px;
}

.stats-card .ant-statistic {
  text-align: center;
}

.stats-card .ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 8px;
}

.stats-card .ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
}

.stats-card .ant-statistic-content-prefix {
  margin-right: 4px;
  font-size: 16px;
}

.stats-card .ant-statistic-content-suffix {
  font-size: 12px;
  color: #8c8c8c;
  margin-left: 4px;
}

/* 操作按钮 */
.action-buttons {
  margin-top: auto;
  padding-top: 20px;
}

.logout-button {
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 77, 79, 0.3);
}

.logout-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 77, 79, 0.4);
}

/* 编辑偏好模态框 */
.edit-preferences-modal .ant-modal-content {
  border-radius: 16px;
}

.edit-preferences-modal .ant-modal-header {
  border-radius: 16px 16px 0 0;
  border-bottom: 1px solid #f0f0f0;
}

.edit-preferences-modal .ant-modal-title {
  font-size: 18px;
  font-weight: 600;
}

.edit-preferences-modal .ant-modal-body {
  padding: 24px;
}

.edit-preferences-modal .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.edit-preferences-modal .ant-select-selector {
  border-radius: 8px;
}

.edit-preferences-modal .ant-select-selection-item {
  border-radius: 6px;
}

.modal-actions {
  margin-bottom: 0;
  text-align: right;
}

.modal-actions .ant-form-item-control-input-content {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.modal-actions .ant-btn {
  border-radius: 8px;
  padding: 0 20px;
  height: 36px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-content {
    padding: 16px;
    gap: 16px;
  }
  
  .user-info-card .ant-card-body {
    padding: 20px;
  }
  
  .user-info-content {
    gap: 16px;
  }
  
  .user-avatar {
    width: 64px;
    height: 64px;
  }
  
  .user-name {
    font-size: 20px;
  }
  
  .edit-button {
    height: 32px;
    padding: 0 12px;
    font-size: 13px;
  }
  
  .preferences-card .ant-card-body,
  .stats-card .ant-card-body {
    padding: 16px 20px;
  }
  
  .stats-card .ant-statistic-content {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .status-bar {
    padding: 0 16px;
  }
  
  .page-content {
    padding: 12px;
    gap: 12px;
  }
  
  .user-info-card .ant-card-body {
    padding: 16px;
  }
  
  .user-info-content {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .user-avatar {
    width: 60px;
    height: 60px;
  }
  
  .user-name {
    font-size: 18px;
  }
  
  .edit-button {
    width: 100%;
  }
  
  .preferences-card .ant-card-body,
  .stats-card .ant-card-body {
    padding: 12px 16px;
  }
  
  .preference-section .ant-typography {
    font-size: 13px;
  }
  
  .preference-tag {
    font-size: 11px;
    padding: 3px 6px;
  }
  
  .stats-card .ant-statistic-content {
    font-size: 16px;
  }
  
  .stats-card .ant-statistic-title {
    font-size: 11px;
  }
  
  .logout-button {
    height: 44px;
    font-size: 14px;
  }
  
  .edit-preferences-modal .ant-modal-body {
    padding: 20px;
  }
}

/* 动画效果 */
.user-info-card,
.preferences-card,
.stats-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.page-content::-webkit-scrollbar {
  width: 4px;
}

.page-content::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.page-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.page-content::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 标签悬停效果 */
.preference-tag:hover {
  transform: scale(1.05);
  transition: transform 0.2s ease;
}

/* 统计卡片悬停效果 */
.stats-card .ant-statistic:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}
