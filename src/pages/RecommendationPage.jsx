/**
 * 推荐页面组件
 * 功能：个性化推荐、每日推荐、情绪推荐、推荐历史
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Tag, message, Spin, Empty, Tabs, Select } from 'antd';
import { 
  PlayCircleOutlined, 
  PauseCircleOutlined, 
  HeartOutlined,
  HeartFilled,
  ReloadOutlined,
  HistoryOutlined,
  ThunderboltOutlined,
  SoundOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { recommendationAPI, songAPI } from '../utils/api';
import BottomNavigation from '../components/BottomNavigation';
import './RecommendationPage.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const RecommendationPage = ({ user, onPlaySong, currentSong, isPlaying }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('personalized');
  const [personalizedSongs, setPersonalizedSongs] = useState([]);
  const [dailySongs, setDailySongs] = useState([]);
  const [moodSongs, setMoodSongs] = useState([]);
  const [selectedMood, setSelectedMood] = useState('mysterious');
  const [loading, setLoading] = useState(false);
  const [likedSongs, setLikedSongs] = useState(new Set());

  // 情绪选项
  const moodOptions = [
    { value: 'mysterious', label: '神秘', color: '#722ed1' },
    { value: 'calm', label: '平静', color: '#52c41a' },
    { value: 'energetic', label: '活力', color: '#fa541c' },
    { value: 'romantic', label: '浪漫', color: '#eb2f96' },
    { value: 'happy', label: '快乐', color: '#faad14' }
  ];

  // 加载推荐数据
  useEffect(() => {
    if (activeTab === 'personalized') {
      loadPersonalizedRecommendations();
    } else if (activeTab === 'daily') {
      loadDailyRecommendations();
    } else if (activeTab === 'mood') {
      loadMoodRecommendations();
    }
  }, [activeTab, selectedMood, user]);

  // 获取个性化推荐
  const loadPersonalizedRecommendations = async () => {
    try {
      setLoading(true);
      const response = await recommendationAPI.getPersonalized(
        user.id, 
        user.preferences, 
        10
      );
      
      if (response.success) {
        setPersonalizedSongs(response.data.recommendations);
      }
    } catch (error) {
      console.error('获取个性化推荐失败:', error);
      message.error('获取推荐失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取每日推荐
  const loadDailyRecommendations = async () => {
    try {
      setLoading(true);
      const response = await recommendationAPI.getDaily(user.id, 10);
      
      if (response.success) {
        setDailySongs(response.data.recommendations);
      }
    } catch (error) {
      console.error('获取每日推荐失败:', error);
      message.error('获取每日推荐失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取情绪推荐
  const loadMoodRecommendations = async () => {
    try {
      setLoading(true);
      const response = await recommendationAPI.getMoodRecommendations(
        selectedMood, 
        user.id, 
        10
      );
      
      if (response.success) {
        setMoodSongs(response.data.recommendations);
      }
    } catch (error) {
      console.error('获取情绪推荐失败:', error);
      message.error('获取情绪推荐失败');
    } finally {
      setLoading(false);
    }
  };

  // 播放歌曲
  const handlePlaySong = async (song) => {
    try {
      await songAPI.recordPlay(song.id, 0);
      onPlaySong(song);
    } catch (error) {
      console.error('播放失败:', error);
      message.error('播放失败');
    }
  };

  // 点赞歌曲
  const handleLikeSong = async (song, e) => {
    e.stopPropagation();
    
    try {
      const isLiked = likedSongs.has(song.id);
      const action = isLiked ? 'unlike' : 'like';
      
      await songAPI.likeSong(song.id, action);
      
      const newLikedSongs = new Set(likedSongs);
      if (isLiked) {
        newLikedSongs.delete(song.id);
        message.success('取消点赞');
      } else {
        newLikedSongs.add(song.id);
        message.success('点赞成功');
      }
      
      setLikedSongs(newLikedSongs);
    } catch (error) {
      console.error('点赞失败:', error);
      message.error('操作失败');
    }
  };

  // 刷新推荐
  const handleRefresh = () => {
    if (activeTab === 'personalized') {
      loadPersonalizedRecommendations();
    } else if (activeTab === 'daily') {
      loadDailyRecommendations();
    } else if (activeTab === 'mood') {
      loadMoodRecommendations();
    }
  };

  // 跳转到问卷页面
  const handleGoToSurvey = (songId) => {
    navigate(`/survey/${songId}`);
  };

  // 格式化时长
  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  // 歌曲卡片组件
  const SongCard = ({ song, showSurveyButton = false }) => {
    const isCurrentSong = currentSong && currentSong.id === song.id;
    const isLiked = likedSongs.has(song.id);
    
    return (
      <Card 
        className={`recommendation-song-card ${isCurrentSong ? 'playing' : ''}`}
        onClick={() => handlePlaySong(song)}
        hoverable
      >
        <div className="song-content">
          <div className="song-cover">
            <img 
              src={song.coverUrl || '/placeholder-cover.jpg'} 
              alt={song.title}
              onError={(e) => {
                e.target.src = '/placeholder-cover.jpg';
              }}
            />
            <div className="play-overlay">
              {isCurrentSong && isPlaying ? (
                <PauseCircleOutlined className="play-icon" />
              ) : (
                <PlayCircleOutlined className="play-icon" />
              )}
            </div>
          </div>
          
          <div className="song-info">
            <Title level={5} className="song-title">{song.title}</Title>
            <Text className="song-artist">{song.artist}</Text>
            <div className="song-meta">
              <Text type="secondary">{formatDuration(song.duration)}</Text>
              <Text type="secondary">•</Text>
              <Text type="secondary">{song.playCount} 播放</Text>
            </div>
            <div className="song-tags">
              {song.tags && song.tags.slice(0, 2).map(tag => (
                <Tag key={tag} size="small">{tag}</Tag>
              ))}
            </div>
            {song.recommendationReason && (
              <Text className="recommendation-reason" type="secondary">
                {song.recommendationReason}
              </Text>
            )}
          </div>
          
          <div className="song-actions">
            <Button
              type="text"
              icon={isLiked ? <HeartFilled /> : <HeartOutlined />}
              className={`like-button ${isLiked ? 'liked' : ''}`}
              onClick={(e) => handleLikeSong(song, e)}
            />
            {showSurveyButton && (
              <Button
                type="primary"
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleGoToSurvey(song.id);
                }}
              >
                评价
              </Button>
            )}
          </div>
        </div>
      </Card>
    );
  };

  // 渲染歌曲列表
  const renderSongList = (songs, showSurveyButton = false) => {
    if (loading) {
      return (
        <div className="loading-container">
          <Spin size="large" />
          <Text>加载推荐中...</Text>
        </div>
      );
    }

    if (songs.length === 0) {
      return (
        <Empty 
          description="暂无推荐内容"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      );
    }

    return (
      <div className="recommendation-songs-list">
        {songs.map(song => (
          <SongCard 
            key={song.id} 
            song={song} 
            showSurveyButton={showSurveyButton}
          />
        ))}
      </div>
    );
  };

  return (
    <div className="recommendation-page">
      {/* 顶部状态栏 */}
      <div className="status-bar">
        <div className="status-left">
          <SoundOutlined />
          <span>推荐</span>
        </div>
        <div className="status-right">
          <Button 
            type="text" 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
            loading={loading}
          />
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-container">
        <div className="page-content">
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            className="recommendation-tabs"
          >
            <TabPane 
              tab={
                <span>
                  <ThunderboltOutlined />
                  个性化
                </span>
              } 
              key="personalized"
            >
              <div className="tab-content">
                <div className="tab-header">
                  <Title level={4}>为你推荐</Title>
                  <Text type="secondary">基于你的音乐偏好</Text>
                </div>
                {renderSongList(personalizedSongs, true)}
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span>
                  <SoundOutlined />
                  每日推荐
                </span>
              } 
              key="daily"
            >
              <div className="tab-content">
                <div className="tab-header">
                  <Title level={4}>今日精选</Title>
                  <Text type="secondary">每天为你精心挑选</Text>
                </div>
                {renderSongList(dailySongs)}
              </div>
            </TabPane>
            
            <TabPane 
              tab={
                <span>
                  <HeartOutlined />
                  情绪推荐
                </span>
              } 
              key="mood"
            >
              <div className="tab-content">
                <div className="tab-header">
                  <Title level={4}>情绪音乐</Title>
                  <div className="mood-selector">
                    <Text type="secondary">选择你的心情：</Text>
                    <Select
                      value={selectedMood}
                      onChange={setSelectedMood}
                      style={{ width: 120, marginLeft: 8 }}
                    >
                      {moodOptions.map(mood => (
                        <Option key={mood.value} value={mood.value}>
                          <span style={{ color: mood.color }}>
                            {mood.label}
                          </span>
                        </Option>
                      ))}
                    </Select>
                  </div>
                </div>
                {renderSongList(moodSongs)}
              </div>
            </TabPane>
          </Tabs>
        </div>
      </div>

      {/* 底部导航 */}
      <BottomNavigation currentPath="/recommendations" />
    </div>
  );
};

export default RecommendationPage;
