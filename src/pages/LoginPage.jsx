/**
 * 登录页面组件
 * 功能：手机号验证码登录、用户注册
 */

import React, { useState, useEffect } from 'react';
import { Button, Input, Form, message, Space, Typography, Card } from 'antd';
import { MobileOutlined, SafetyOutlined, SoundOutlined } from '@ant-design/icons';
import { authAPI } from '../utils/api';
import { saveToken, saveUser, validatePhone } from '../utils/auth';
import './LoginPage.css';

const { Title, Text } = Typography;

const LoginPage = ({ onLogin }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [sendingCode, setSendingCode] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 倒计时效果
  useEffect(() => {
    let timer;
    if (countdown > 0) {
      timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
    }
    return () => clearTimeout(timer);
  }, [countdown]);

  // 发送验证码
  const handleSendCode = async () => {
    try {
      const phone = form.getFieldValue('phone');
      
      if (!phone) {
        message.error('请输入手机号');
        return;
      }
      
      if (!validatePhone(phone)) {
        message.error('请输入正确的手机号格式');
        return;
      }

      setSendingCode(true);
      
      const response = await authAPI.sendCode(phone);
      
      if (response.success) {
        message.success('验证码已发送');
        setCountdown(60); // 60秒倒计时
      } else {
        message.error(response.message || '发送验证码失败');
      }
      
    } catch (error) {
      console.error('发送验证码失败:', error);
      message.error(error.message || '发送验证码失败');
    } finally {
      setSendingCode(false);
    }
  };

  // 登录处理
  const handleLogin = async (values) => {
    try {
      setLoading(true);
      
      const { phone, verificationCode } = values;
      
      const response = await authAPI.phoneLogin(phone, verificationCode);
      
      if (response.success) {
        // 保存token和用户信息
        saveToken(response.data.token);
        saveUser(response.data.user);
        
        message.success(response.message || '登录成功');
        
        // 调用父组件的登录回调
        onLogin(response.data.user);
        
      } else {
        message.error(response.message || '登录失败');
      }
      
    } catch (error) {
      console.error('登录失败:', error);
      message.error(error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  // 手机号格式化
  const formatPhoneNumber = (value) => {
    if (!value) return value;
    
    // 移除所有非数字字符
    const phoneNumber = value.replace(/[^\d]/g, '');
    
    // 限制长度为11位
    if (phoneNumber.length <= 11) {
      return phoneNumber;
    }
    
    return phoneNumber.slice(0, 11);
  };

  return (
    <div className="login-page">
      {/* 背景装饰 */}
      <div className="login-background">
        <div className="wave wave1"></div>
        <div className="wave wave2"></div>
        <div className="wave wave3"></div>
      </div>
      
      {/* 顶部Logo区域 */}
      <div className="login-header">
        <div className="logo-container">
          <SoundOutlined className="logo-icon" />
          <Title level={1} className="app-title">壹人音</Title>
          <Text className="app-subtitle">专属你的音乐体验</Text>
        </div>
      </div>

      {/* 登录表单 */}
      <div className="login-form-container">
        <Card className="login-card">
          <div className="form-header">
            <Title level={3}>欢迎使用</Title>
            <Text type="secondary">请输入手机号码登录或注册</Text>
          </div>

          <Form
            form={form}
            layout="vertical"
            onFinish={handleLogin}
            autoComplete="off"
            size="large"
          >
            <Form.Item
              name="phone"
              label="手机号码"
              rules={[
                { required: true, message: '请输入手机号码' },
                { 
                  validator: (_, value) => {
                    if (!value || validatePhone(value)) {
                      return Promise.resolve();
                    }
                    return Promise.reject(new Error('请输入正确的手机号格式'));
                  }
                }
              ]}
            >
              <Input
                prefix={<MobileOutlined />}
                placeholder="请输入11位手机号码"
                maxLength={11}
                onChange={(e) => {
                  const formatted = formatPhoneNumber(e.target.value);
                  form.setFieldsValue({ phone: formatted });
                }}
              />
            </Form.Item>

            <Form.Item
              name="verificationCode"
              label="验证码"
              rules={[
                { required: true, message: '请输入验证码' },
                { len: 4, message: '验证码为4位数字' }
              ]}
            >
              <Space.Compact style={{ width: '100%' }}>
                <Input
                  prefix={<SafetyOutlined />}
                  placeholder="请输入4位验证码"
                  maxLength={4}
                  style={{ flex: 1 }}
                />
                <Button
                  type="default"
                  onClick={handleSendCode}
                  loading={sendingCode}
                  disabled={countdown > 0}
                  style={{ width: '120px' }}
                >
                  {countdown > 0 ? `${countdown}s后重发` : '获取验证码'}
                </Button>
              </Space.Compact>
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }}>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                className="login-button"
              >
                {loading ? '登录中...' : '登录 / 注册'}
              </Button>
            </Form.Item>
          </Form>

          <div className="login-tips">
            <Text type="secondary" className="tip-text">
              • 新用户首次登录将自动注册账号
            </Text>
            <Text type="secondary" className="tip-text">
              • 验证码有效期为5分钟
            </Text>
            <Text type="secondary" className="tip-text">
              • 登录即表示同意用户协议和隐私政策
            </Text>
          </div>
        </Card>
      </div>

      {/* 底部装饰 */}
      <div className="login-footer">
        <Text type="secondary">
          © 2024 壹人音 · 让音乐更懂你
        </Text>
      </div>
    </div>
  );
};

export default LoginPage;
