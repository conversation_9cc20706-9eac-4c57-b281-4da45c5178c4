/**
 * 推荐页面样式
 * 现代化移动端设计，标签页布局，流畅动画
 */

.recommendation-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 200px;
  gap: 16px;
}

/* 顶部状态栏 */
.status-bar {
  height: 44px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  color: white;
  font-size: 14px;
  font-weight: 500;
}

.status-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-right .ant-btn {
  color: white;
  border: none;
  background: transparent;
}

.status-right .ant-btn:hover {
  background: rgba(255, 255, 255, 0.1);
}

/* 页面容器 */
.page-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 20px 20px 0 0;
  overflow: hidden;
}

.page-content {
  flex: 1;
  overflow: hidden;
}

/* 标签页样式 */
.recommendation-tabs {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.recommendation-tabs .ant-tabs-nav {
  margin: 0;
  padding: 0 20px;
  background: white;
  border-bottom: 1px solid #f0f0f0;
}

.recommendation-tabs .ant-tabs-tab {
  padding: 16px 0;
  margin: 0 16px 0 0;
  font-weight: 500;
}

.recommendation-tabs .ant-tabs-tab-active {
  color: #1890ff;
}

.recommendation-tabs .ant-tabs-content-holder {
  flex: 1;
  overflow: hidden;
}

.recommendation-tabs .ant-tabs-content {
  height: 100%;
}

.recommendation-tabs .ant-tabs-tabpane {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 标签页内容 */
.tab-content {
  padding: 20px;
  height: 100%;
}

.tab-header {
  margin-bottom: 24px;
}

.tab-header h4 {
  margin: 0 0 4px 0;
  color: #262626;
  font-weight: 600;
}

.mood-selector {
  display: flex;
  align-items: center;
  margin-top: 8px;
}

/* 推荐歌曲列表 */
.recommendation-songs-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 推荐歌曲卡片 */
.recommendation-song-card {
  border-radius: 16px;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.recommendation-song-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.recommendation-song-card.playing {
  background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
  border: 1px solid #1890ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.2);
}

.recommendation-song-card .ant-card-body {
  padding: 16px;
}

.song-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* 歌曲封面 */
.song-cover {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  background: #f5f5f5;
}

.song-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all 0.3s ease;
}

.recommendation-song-card:hover .play-overlay {
  opacity: 1;
}

.play-icon {
  color: white;
  font-size: 24px;
}

/* 歌曲信息 */
.song-info {
  flex: 1;
  min-width: 0;
}

.song-title {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.song-artist {
  display: block;
  font-size: 14px;
  color: #8c8c8c;
  margin-bottom: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.song-meta {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
}

.song-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-bottom: 8px;
}

.song-tags .ant-tag {
  margin: 0;
  border-radius: 8px;
  font-size: 11px;
  padding: 2px 6px;
  background: #f0f0f0;
  border: none;
  color: #666;
}

.recommendation-reason {
  display: block;
  font-size: 12px;
  font-style: italic;
  margin-top: 4px;
  color: #1890ff;
}

/* 歌曲操作 */
.song-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.like-button {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.like-button:hover {
  background: #f5f5f5;
  transform: scale(1.1);
}

.like-button.liked {
  color: #ff4d4f;
}

.like-button.liked:hover {
  background: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .tab-content {
    padding: 16px;
  }
  
  .tab-header {
    margin-bottom: 20px;
  }
  
  .song-content {
    gap: 12px;
  }
  
  .song-cover {
    width: 50px;
    height: 50px;
  }
  
  .song-title {
    font-size: 15px;
  }
  
  .song-artist {
    font-size: 13px;
  }
  
  .play-icon {
    font-size: 20px;
  }
}

@media (max-width: 480px) {
  .status-bar {
    padding: 0 16px;
  }
  
  .recommendation-tabs .ant-tabs-nav {
    padding: 0 16px;
  }
  
  .recommendation-tabs .ant-tabs-tab {
    margin-right: 12px;
    padding: 12px 0;
  }
  
  .tab-content {
    padding: 12px;
  }
  
  .recommendation-song-card .ant-card-body {
    padding: 12px;
  }
  
  .song-content {
    gap: 10px;
  }
  
  .song-cover {
    width: 45px;
    height: 45px;
    border-radius: 8px;
  }
  
  .song-title {
    font-size: 14px;
  }
  
  .song-artist {
    font-size: 12px;
  }
  
  .song-meta {
    font-size: 11px;
  }
  
  .mood-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .mood-selector .ant-select {
    width: 100px !important;
    margin-left: 0 !important;
  }
}

/* 动画效果 */
.recommendation-song-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 空状态样式 */
.ant-empty {
  padding: 40px 20px;
}

.ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* 滚动条样式 */
.ant-tabs-tabpane::-webkit-scrollbar {
  width: 4px;
}

.ant-tabs-tabpane::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.ant-tabs-tabpane::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 2px;
}

.ant-tabs-tabpane::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 选择器样式 */
.mood-selector .ant-select-selector {
  border-radius: 8px;
}

.mood-selector .ant-select-selection-item {
  font-weight: 500;
}
