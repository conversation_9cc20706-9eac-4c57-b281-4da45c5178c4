/**
 * 个人资料页面组件
 * 功能：用户信息展示、偏好设置、统计数据、退出登录
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Avatar, Tag, Statistic, Row, Col, Modal, Form, Select, message } from 'antd';
import { 
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  MusicNoteOutlined,
  HeartOutlined,
  TrophyOutlined,
  CalendarOutlined,
  EditOutlined
} from '@ant-design/icons';
import { userAPI } from '../utils/api';
import BottomNavigation from '../components/BottomNavigation';
import './ProfilePage.css';

const { Title, Text } = Typography;
const { Option } = Select;

const ProfilePage = ({ user, onLogout }) => {
  const [userStats, setUserStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [form] = Form.useForm();

  // 音乐类型选项
  const genreOptions = [
    { value: 'ambient', label: '氛围音乐', color: '#722ed1' },
    { value: 'electronic', label: '电子音乐', color: '#1890ff' },
    { value: 'pop', label: '流行音乐', color: '#52c41a' },
    { value: 'rock', label: '摇滚音乐', color: '#fa541c' },
    { value: 'jazz', label: '爵士音乐', color: '#faad14' },
    { value: 'classical', label: '古典音乐', color: '#eb2f96' }
  ];

  // 情绪选项
  const moodOptions = [
    { value: 'mysterious', label: '神秘', color: '#722ed1' },
    { value: 'calm', label: '平静', color: '#52c41a' },
    { value: 'energetic', label: '活力', color: '#fa541c' },
    { value: 'romantic', label: '浪漫', color: '#eb2f96' },
    { value: 'happy', label: '快乐', color: '#faad14' }
  ];

  // 加载用户统计数据
  useEffect(() => {
    loadUserStats();
  }, [user]);

  const loadUserStats = async () => {
    try {
      setLoading(true);
      const response = await userAPI.getStats(user.id);
      
      if (response.success) {
        setUserStats(response.data.stats);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开编辑偏好模态框
  const handleEditPreferences = () => {
    form.setFieldsValue({
      genres: user.preferences.genres || [],
      moods: user.preferences.moods || []
    });
    setEditModalVisible(true);
  };

  // 保存偏好设置
  const handleSavePreferences = async (values) => {
    try {
      const response = await userAPI.updatePreferences(user.id, values);
      
      if (response.success) {
        message.success('偏好设置已更新');
        setEditModalVisible(false);
        // 这里应该更新全局用户状态，但为了简化，我们只关闭模态框
      } else {
        message.error(response.message || '更新失败');
      }
    } catch (error) {
      console.error('更新偏好失败:', error);
      message.error('更新失败');
    }
  };

  // 退出登录确认
  const handleLogoutConfirm = () => {
    Modal.confirm({
      title: '确认退出',
      content: '您确定要退出登录吗？',
      okText: '确定',
      cancelText: '取消',
      onOk: onLogout
    });
  };

  // 获取偏好标签颜色
  const getPreferenceColor = (type, value) => {
    const options = type === 'genre' ? genreOptions : moodOptions;
    const option = options.find(opt => opt.value === value);
    return option ? option.color : '#1890ff';
  };

  // 获取偏好标签文本
  const getPreferenceLabel = (type, value) => {
    const options = type === 'genre' ? genreOptions : moodOptions;
    const option = options.find(opt => opt.value === value);
    return option ? option.label : value;
  };

  return (
    <div className="profile-page">
      {/* 顶部状态栏 */}
      <div className="status-bar">
        <div className="status-left">
          <UserOutlined />
          <span>我的</span>
        </div>
        <div className="status-right">
          <Button 
            type="text" 
            icon={<SettingOutlined />} 
            onClick={handleEditPreferences}
          />
        </div>
      </div>

      {/* 页面内容 */}
      <div className="page-container">
        <div className="page-content">
          {/* 用户信息卡片 */}
          <Card className="user-info-card">
            <div className="user-info-content">
              <Avatar 
                size={80} 
                icon={<UserOutlined />} 
                className="user-avatar"
              />
              <div className="user-details">
                <Title level={3} className="user-name">{user.nickname}</Title>
                <Text className="user-phone">{user.phone}</Text>
                <div className="user-badges">
                  <Tag color="blue" icon={<MusicNoteOutlined />}>
                    音乐爱好者
                  </Tag>
                </div>
              </div>
              <Button 
                type="primary" 
                icon={<EditOutlined />}
                onClick={handleEditPreferences}
                className="edit-button"
              >
                编辑偏好
              </Button>
            </div>
          </Card>

          {/* 音乐偏好卡片 */}
          <Card className="preferences-card" title="音乐偏好">
            <div className="preference-section">
              <Text strong>喜欢的音乐类型：</Text>
              <div className="preference-tags">
                {user.preferences.genres && user.preferences.genres.length > 0 ? (
                  user.preferences.genres.map(genre => (
                    <Tag 
                      key={genre} 
                      color={getPreferenceColor('genre', genre)}
                      className="preference-tag"
                    >
                      {getPreferenceLabel('genre', genre)}
                    </Tag>
                  ))
                ) : (
                  <Text type="secondary">暂未设置</Text>
                )}
              </div>
            </div>
            
            <div className="preference-section">
              <Text strong>喜欢的情绪：</Text>
              <div className="preference-tags">
                {user.preferences.moods && user.preferences.moods.length > 0 ? (
                  user.preferences.moods.map(mood => (
                    <Tag 
                      key={mood} 
                      color={getPreferenceColor('mood', mood)}
                      className="preference-tag"
                    >
                      {getPreferenceLabel('mood', mood)}
                    </Tag>
                  ))
                ) : (
                  <Text type="secondary">暂未设置</Text>
                )}
              </div>
            </div>
          </Card>

          {/* 统计数据卡片 */}
          {userStats && (
            <Card className="stats-card" title="我的统计">
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Statistic
                    title="听歌时长"
                    value={userStats.totalListeningTime}
                    suffix="分钟"
                    prefix={<MusicNoteOutlined />}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="听过歌曲"
                    value={userStats.totalSongs}
                    suffix="首"
                    prefix={<HeartOutlined />}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="平均评分"
                    value={userStats.averageRating}
                    precision={1}
                    suffix="分"
                    prefix={<TrophyOutlined />}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="加入天数"
                    value={userStats.joinDays}
                    suffix="天"
                    prefix={<CalendarOutlined />}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Col>
              </Row>
            </Card>
          )}

          {/* 操作按钮 */}
          <div className="action-buttons">
            <Button 
              type="primary" 
              danger 
              icon={<LogoutOutlined />}
              onClick={handleLogoutConfirm}
              className="logout-button"
              block
            >
              退出登录
            </Button>
          </div>
        </div>
      </div>

      {/* 编辑偏好模态框 */}
      <Modal
        title="编辑音乐偏好"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        footer={null}
        className="edit-preferences-modal"
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePreferences}
        >
          <Form.Item
            name="genres"
            label="喜欢的音乐类型"
            rules={[{ required: true, message: '请选择至少一种音乐类型' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择您喜欢的音乐类型"
              maxTagCount={3}
            >
              {genreOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <span style={{ color: option.color }}>
                    {option.label}
                  </span>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="moods"
            label="喜欢的情绪"
            rules={[{ required: true, message: '请选择至少一种情绪' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择您喜欢的情绪"
              maxTagCount={3}
            >
              {moodOptions.map(option => (
                <Option key={option.value} value={option.value}>
                  <span style={{ color: option.color }}>
                    {option.label}
                  </span>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item className="modal-actions">
            <Button onClick={() => setEditModalVisible(false)}>
              取消
            </Button>
            <Button type="primary" htmlType="submit">
              保存
            </Button>
          </Form.Item>
        </Form>
      </Modal>

      {/* 底部导航 */}
      <BottomNavigation currentPath="/profile" />
    </div>
  );
};

export default ProfilePage;
