-- 壹人音系统数据库初始化脚本
-- 创建时间: 2025-07-30
-- 版本: v1.0

-- 创建数据库
CREATE DATABASE IF NOT EXISTS yiren_music 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE yiren_music;

-- 1. 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '用户ID',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号',
    nickname VARCHAR(50) DEFAULT NULL COMMENT '昵称',
    avatar VARCHAR(255) DEFAULT NULL COMMENT '头像URL',
    gender TINYINT DEFAULT 0 COMMENT '性别: 0-未知, 1-男, 2-女',
    age TINYINT DEFAULT NULL COMMENT '年龄',
    city VARCHAR(50) DEFAULT NULL COMMENT '城市',
    preferences JSON DEFAULT NULL COMMENT '音乐偏好(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_phone (phone),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 2. 歌曲表
CREATE TABLE songs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '歌曲ID',
    title VARCHAR(200) NOT NULL COMMENT '歌曲标题',
    artist VARCHAR(100) NOT NULL COMMENT '艺术家',
    album VARCHAR(200) DEFAULT NULL COMMENT '专辑名称',
    duration INT DEFAULT NULL COMMENT '时长(秒)',
    cover_url VARCHAR(500) DEFAULT NULL COMMENT '封面图片URL',
    audio_url VARCHAR(500) NOT NULL COMMENT '音频文件URL',
    genre VARCHAR(50) DEFAULT NULL COMMENT '音乐风格',
    mood VARCHAR(50) DEFAULT NULL COMMENT '情绪标签',
    tags JSON DEFAULT NULL COMMENT '标签(JSON数组)',
    play_count INT DEFAULT 0 COMMENT '播放次数',
    like_count INT DEFAULT 0 COMMENT '喜欢次数',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-下架, 1-正常',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_artist (artist),
    INDEX idx_genre (genre),
    INDEX idx_mood (mood),
    INDEX idx_status (status),
    INDEX idx_play_count (play_count DESC),
    INDEX idx_genre_mood (genre, mood, status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='歌曲表';

-- 3. 用户歌曲推荐记录表
CREATE TABLE user_song_recommendations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '推荐记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    song_id BIGINT NOT NULL COMMENT '歌曲ID',
    recommendation_type VARCHAR(50) NOT NULL COMMENT '推荐类型: daily, mood, preference',
    recommendation_score DECIMAL(3,2) DEFAULT NULL COMMENT '推荐分数(0-1)',
    is_played TINYINT DEFAULT 0 COMMENT '是否播放: 0-否, 1-是',
    is_liked TINYINT DEFAULT 0 COMMENT '是否喜欢: 0-否, 1-是',
    play_duration INT DEFAULT 0 COMMENT '播放时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '推荐时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_song_id (song_id),
    INDEX idx_recommendation_type (recommendation_type),
    INDEX idx_created_at (created_at),
    INDEX idx_user_created (user_id, created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户歌曲推荐记录表';

-- 4. 问卷模板表
CREATE TABLE survey_templates (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '问卷模板ID',
    name VARCHAR(100) NOT NULL COMMENT '问卷名称',
    description TEXT DEFAULT NULL COMMENT '问卷描述',
    questions JSON NOT NULL COMMENT '问题配置(JSON格式)',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='问卷模板表';

-- 5. 用户问卷回答表
CREATE TABLE user_survey_responses (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '回答记录ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    song_id BIGINT NOT NULL COMMENT '歌曲ID',
    template_id BIGINT NOT NULL COMMENT '问卷模板ID',
    responses JSON NOT NULL COMMENT '回答内容(JSON格式)',
    overall_rating TINYINT DEFAULT NULL COMMENT '总体评分(1-5)',
    completion_time INT DEFAULT NULL COMMENT '完成时长(秒)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    
    INDEX idx_user_id (user_id),
    INDEX idx_song_id (song_id),
    INDEX idx_template_id (template_id),
    INDEX idx_overall_rating (overall_rating),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (song_id) REFERENCES songs(id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES survey_templates(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户问卷回答表';

-- 6. 管理员表
CREATE TABLE admins (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '管理员ID',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码(加密)',
    real_name VARCHAR(50) DEFAULT NULL COMMENT '真实姓名',
    email VARCHAR(100) DEFAULT NULL COMMENT '邮箱',
    role VARCHAR(20) DEFAULT 'admin' COMMENT '角色: admin, super_admin',
    permissions JSON DEFAULT NULL COMMENT '权限配置',
    status TINYINT DEFAULT 1 COMMENT '状态: 0-禁用, 1-正常',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_username (username),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 插入初始数据

-- 插入默认管理员账户 (密码: admin123)
INSERT INTO admins (username, password, real_name, role, permissions) VALUES 
('admin', '$2b$10$rOzJqQjQjQjQjQjQjQjQjOzJqQjQjQjQjQjQjQjQjQjQjQjQjQjQjQ', '系统管理员', 'super_admin', 
'["user_manage", "song_manage", "survey_manage", "data_analysis", "system_config"]');

-- 插入默认问卷模板
INSERT INTO survey_templates (name, description, questions) VALUES 
('歌曲体验问卷', '用于收集用户对推荐歌曲的反馈', 
'{
  "questions": [
    {
      "id": 1,
      "type": "rating",
      "title": "喜欢这个曲子吗？",
      "options": ["A", "B", "C"],
      "required": true
    },
    {
      "id": 2,
      "type": "multiple_choice",
      "title": "音响设备评价",
      "options": ["音质很好", "音量适中", "音效清晰", "有杂音"],
      "required": true
    },
    {
      "id": 3,
      "type": "rating",
      "title": "环境体验评分",
      "scale": 5,
      "required": true
    },
    {
      "id": 4,
      "type": "rating",
      "title": "技师服务评价",
      "scale": 5,
      "required": true
    },
    {
      "id": 5,
      "type": "rating",
      "title": "整体满意度评分",
      "scale": 5,
      "required": true
    },
    {
      "id": 6,
      "type": "text",
      "title": "改进建议",
      "required": false
    }
  ]
}');

-- 插入示例歌曲数据
INSERT INTO songs (title, artist, album, duration, cover_url, audio_url, genre, mood, tags) VALUES 
('Eye of Horus', 'Unknown Artist', 'Desert Winds', 240, '/images/covers/eye-of-horus.jpg', '/audio/eye-of-horus.mp3', 'ambient', 'mysterious', '["沙漠", "神秘", "古埃及", "冥想"]'),
('Neon Dreams', 'Cyber Artist', 'Future Sounds', 180, '/images/covers/neon-dreams.jpg', '/audio/neon-dreams.mp3', 'electronic', 'energetic', '["科技", "未来", "电子", "活力"]'),
('Peaceful Morning', 'Nature Sounds', 'Calm Collection', 300, '/images/covers/peaceful-morning.jpg', '/audio/peaceful-morning.mp3', 'ambient', 'calm', '["自然", "平静", "早晨", "放松"]');

-- 创建视图：用户统计
CREATE VIEW user_stats AS
SELECT 
    u.id,
    u.phone,
    u.nickname,
    COUNT(DISTINCT usr.id) as recommendation_count,
    COUNT(DISTINCT usr.id) as survey_count,
    AVG(usr.overall_rating) as avg_rating,
    u.created_at
FROM users u
LEFT JOIN user_song_recommendations usr ON u.id = usr.user_id
LEFT JOIN user_survey_responses usr ON u.id = usr.user_id
GROUP BY u.id;

-- 创建视图：歌曲统计
CREATE VIEW song_stats AS
SELECT 
    s.id,
    s.title,
    s.artist,
    s.genre,
    s.mood,
    s.play_count,
    s.like_count,
    COUNT(DISTINCT usr.id) as recommendation_count,
    COUNT(DISTINCT usr.id) as survey_count,
    AVG(usr.overall_rating) as avg_rating
FROM songs s
LEFT JOIN user_song_recommendations usr ON s.id = usr.song_id
LEFT JOIN user_survey_responses usr ON s.id = usr.song_id
GROUP BY s.id;

COMMIT;
