# 客户需求分析项目

## 项目概述
本项目基于客户提供的7张图片进行需求分析。客户要求我们认真查看这些图片，真正了解需求之后才能找他进一步沟通。

## 图片文件列表
- 1.JPG - 第一张需求图片
- 2.JPG - 第二张需求图片  
- 3.JPG - 第三张需求图片
- 4.JPG - 第四张需求图片
- 5.JPG - 第五张需求图片
- 6.JPG - 第六张需求图片
- 7.JPG - 第七张需求图片

## 需求分析框架

### 第一步：图片内容识别
请仔细查看每张图片，识别以下内容：
1. **图片类型**：是界面设计图、流程图、功能说明图还是其他类型？
2. **核心信息**：每张图片想要表达的主要内容是什么？
3. **技术要求**：是否涉及特定的技术栈或平台？
4. **功能需求**：图片中展示了哪些具体功能？

### 第二步：需求整理
根据图片内容，整理出：
1. **产品类型**：是网站、移动应用、桌面软件还是其他？
2. **主要功能模块**：列出所有需要实现的功能
3. **用户角色**：涉及哪些用户类型？
4. **业务流程**：核心业务逻辑是什么？
5. **技术架构**：需要什么样的技术实现？

### 第三步：技术实现规划
1. **前端技术选择**：React、Vue、原生开发等
2. **后端技术选择**：Node.js、Python、Java等
3. **数据库设计**：MySQL、MongoDB等
4. **部署方案**：云服务器、CDN等
5. **开发工具**：IDE、版本控制等

### 第四步：项目计划
1. **开发阶段划分**
2. **时间估算**
3. **资源需求**
4. **风险评估**

## 第一张图片分析（1.JPG）- SPA数曲音盒系统需求

### 项目概述
**项目名称**：SPA数曲音盒系统
**项目类型**：单页面应用（SPA）
**核心功能**：音乐播放和用户交互系统

### 核心功能流程（mermaid graph TD）
```
A[输入手机号] --> B[新用户]
B --> C[初始问卷]
B --> D[历史展示-反馈问卷]
C --> E[歌曲推荐]
D --> F
E --> G[SPA体验]
F --> H[体验反馈]
G --> H
```

### 详细页面功能及UI需求

#### 1. 手机号验证页
**功能**：
- 手机号输入及验证
- 新老用户识别（首次/重复到店）
**UI要求**：
- 居中大型数字键盘
- 手机号显示框（11位自动分段：139-1109-1416）
- 动态提示文字："请输入您的专属手机号"
- 背景：系统SPA主题渐变色
- 提交按钮（带指纹图标）

#### 2. 新用户问卷页
**功能**：
- 从80+题库随机抽取5题
- 题目类型：情感/联想选择题（如："看到大海你想到？人自由B.孤独C.冒险"）
- 自动保存答案
**UI要求**：
- 卡片式清动设计（一题一卡）
- 进度条显示（1/5）
- 选项采用语音按钮：A(红)B(蓝)C(绿)
- 题目文字：18px 圆体
- 背景：动态粒子动画（根据当前题目自适化）

#### 3. 老用户欢迎页
**功能**：
- 显示历史记录：上次体验日期数曲
- 展示用户等级徽章（根据到店次数）
- 直接进入体验阶段
**UI要求**：
- 顶部用户信息：手机号+VIP标识
- 历史卡片："上次体验：《森高千夏之歌》2023/07/15"
- 动态欢迎系统："欢迎回到音乐，再体验?次解锁专属歌曲"
- 背景：用户偏好颜色生成的抽象波纹

#### 4. 歌曲推荐页
**功能**：
- 展示推荐歌曲（三大类：电子迷幻/大众流行/古风元素）
- 30秒试听功能（网易云API对接）
- 换歌功能（3次机会）
- 歌曲属性可视化（需求风进度条）

**UI要求**：
- plaintext布局
- 歌曲配置卡片：《完路配菜》今日幸运歌曲
- 专辑封面动态可视化
- 中央乐器选择程度：80%
- 自然感受：80%
- 播放控制条：▷ 00:30
- 操作按钮：[换一首] [确认选择]
- 封面反馈：动态音频可视化（柱状波动画）
- 属性显示：进度条+百分比+图标
- 换歌按钮：剩余次数提示（如"还可更换2次"）

#### 5. SPA后反馈问卷
**功能**：
- 满意度评分（1-5星）
- 技师评价
- 改进建议
- 下次体验意愿

**UI要求**：
- 分区块折叠式设计
- 评分采用交互式星星
- 技师评价：滑动评分条（技术/服务/沟通）
- 建议框：动态增长的文本域
- 提交按钮：按压动效

### 三、后台管理系统功能

#### 1. 用户管理
**数据看板**：
- 用户增长曲线图
- 到店频次分布图
- VIP用户标识（≥10次）

**详情页**：
- 完整体验历史
- 歌曲偏好力图
- 反馈记录时间轴

#### 2. 题库管理
**功能**：
- 题库题库（80+）
- 分类标签：色彩/场景/物品/抽象概念
- 题目状态：启用/禁用
- 反馈跟踪

**管理功能**：
- SPA体验技师评价歌曲满意度
- 随机规则设置
- 新用户：5题随机组合
- 老用户：自动推荐上次问题

#### 3. 歌曲库管理
**歌曲信息**：
- 基础信息（名称/艺术家/时长）
- 三大分类标签
- 自定义属性（温暖度/节奏感等）
- 网易云链接（自动同步新歌）

**更新规则**：
- VIP用户自动更新机制
- 歌曲热度排行榜

#### 4. 数据看板
**实时监控**：
- 当前在线用户
- 今日推荐歌曲TOPS
- 分析报表：
  - 歌曲选择率热力图
  - 用户反馈词云
  - 时段偏好分析

### 四、特殊交互设计

#### 1. 音盒开启动效
- 确认选择后播放3D开盒动画
- 盒内升起歌曲封面+光效

#### 2. 属性可视化
- 动态雷达图（六维属性）
- 进度条颜色渐变值变化（蓝→黄→红）

#### 3. VIP特权
- 10次成就：专属金色界面
- 解锁隐藏歌单
- 优先体验新曲目

#### 4. 多设备适配
- 店铺平板：横屏模式
- 客户手机：竖屏优化
- 后台管理：响应式布局

### 五、视觉规范

#### 1. 主色调
- SPA主题色：#8BC3A3（青绿色）/#D4B483（米金色）

#### 2. 交互规范
- 字体：固体（界面）+ 无线体（后台）
- 动效原则：缓入缓出，时长≤0.3s
- 图标系统：线性图标（音乐相关）

**重要提醒**：所有界面遵循SPA品牌视觉规范，保持放松、治愈的视觉体验，操作反馈即时且直观。

### 技术要求分析
1. **前端**：需要SPA架构（推荐React/Vue）
2. **后端**：用户管理、题库管理、数据统计
3. **数据存储**：用户信息、问卷答案、历史记录、题库
4. **音频处理**：音乐播放功能（网易云API集成）
5. **UI动效**：动态背景、粒子效果、渐变动画、音频可视化、3D开盒动画
6. **响应式设计**：适配手机端、平板端
7. **数据可视化**：图表展示（用户增长、偏好分析、热力图、词云等）
8. **多设备支持**：店铺平板（横屏）+ 客户手机（竖屏）+ 后台管理
9. **品牌视觉**：SPA主题色彩系统、专属字体、图标系统

## 项目开发建议

### 开发优先级
1. **第一阶段**：基础功能开发
   - 用户注册/登录系统
   - 问卷系统（题库+逻辑）
   - 基础歌曲推荐功能
   - 简单的音乐播放器

2. **第二阶段**：核心体验优化
   - 网易云API集成
   - 音频可视化效果
   - 3D开盒动画
   - 用户数据统计

3. **第三阶段**：高级功能
   - 后台管理系统
   - VIP特权系统
   - 数据可视化看板
   - 多设备适配优化

### 技术栈推荐
- **前端**：React + TypeScript + Vite
- **UI框架**：Ant Design + 自定义SPA主题
- **动画库**：Framer Motion + Three.js（3D效果）
- **音频处理**：Web Audio API + Howler.js
- **图表库**：ECharts + D3.js
- **后端**：Node.js + Express + MongoDB
- **部署**：Docker + Nginx

### 六、实际界面设计展示

#### 1. 歌曲推荐结果页
**界面元素**：
- 顶部品牌Logo："壹人音"
- 个性化问候：显示用户手机号码（如：亲爱的13911091416，你今天的幸运歌曲是）
- 圆形歌曲封面展示区域
- 歌曲信息：《Eye of Horus》- 沙漠风格背景
- 播放控制：中央播放按钮
- 歌曲属性标签显示
- 底部操作：使用这个曲子

#### 2. 详细问卷界面
**问卷结构**：
- 标题：你今天体验的是赫鲁兹之音这首歌
- 满意度评分：A/B/C选项
- 详细反馈问题：
  1. 喜欢这个曲子吗？
  2. 音响设备评价（音质/音量等）
  3. 环境体验评分
  4. 技师服务评价
  5. 整体满意度评分
- 开放性问题：改进建议
- 个人信息收集：年龄、性别等
- 提交按钮

#### 3. 音盒开启动画页
**视觉设计**：
- 3D立体音盒效果
- 蓝色科技感背景
- 音盒内部发光效果
- 歌曲封面从音盒中升起
- 粒子特效环绕
- 底部显示：使用这首歌 或者：（选择其他选项）

## 七、技术架构设计

### 1. 技术栈选择

#### 前端技术栈
- **框架**: React 18 + TypeScript
- **构建工具**: Vite
- **UI组件库**: Ant Design + 自定义组件
- **状态管理**: Redux Toolkit
- **路由**: React Router v6
- **动画库**: Framer Motion (3D音盒动画)
- **HTTP客户端**: Axios
- **样式**: Styled-components + CSS Modules

#### 后端技术栈
- **运行环境**: Node.js 18+
- **框架**: Express.js
- **语言**: TypeScript
- **数据库**: MySQL 8.0 (主数据) + Redis (缓存)
- **ORM**: Prisma
- **身份验证**: JWT + bcrypt
- **文件上传**: Multer
- **API文档**: Swagger/OpenAPI

#### 部署与运维
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **进程管理**: PM2
- **监控**: 日志系统 + 性能监控
- **部署**: 云服务器 (阿里云/腾讯云)

### 2. 系统架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   用户端 (Web)   │    │   管理后台      │    │   移动端 (H5)   │
│   React + TS    │    │   React Admin   │    │   响应式设计    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────┴─────────────┐
                    │      API Gateway          │
                    │      (Express.js)         │
                    └─────────────┬─────────────┘
                                 │
          ┌──────────────────────┼──────────────────────┐
          │                      │                      │
    ┌─────┴─────┐        ┌─────┴─────┐        ┌─────┴─────┐
    │ 用户服务   │        │ 歌曲服务   │        │ 问卷服务   │
    │ User API  │        │ Music API │        │Survey API │
    └─────┬─────┘        └─────┬─────┘        └─────┬─────┘
          │                    │                    │
          └────────────────────┼────────────────────┘
                              │
                    ┌─────────┴─────────┐
                    │   数据层           │
                    │ MySQL + Redis     │
                    └───────────────────┘
```

### 3. 核心模块设计

#### 用户模块
- 用户注册/登录
- 手机号验证
- 用户画像分析
- 偏好设置管理

#### 歌曲推荐模块
- 智能推荐算法
- 歌曲库管理
- 播放统计
- 个性化标签

#### 问卷调研模块
- 动态问卷生成
- 多维度评分
- 数据收集分析
- 反馈统计

#### 管理后台模块
- 用户管理
- 歌曲管理
- 问卷管理
- 数据报表

## 八、项目进展总结

### 已完成的工作
1. ✅ **需求分析** - 完整分析了壹人音系统的功能需求和界面设计
2. ✅ **技术架构设计** - 确定了React + Node.js + MySQL的技术栈
3. ✅ **数据库设计** - 完成了6个核心数据表的设计和ER图
4. 🔄 **前端项目初始化** - 正在创建React项目结构

### 核心功能模块
- **歌曲推荐系统**: 基于用户画像的智能推荐算法
- **问卷调研系统**: 多维度用户反馈收集和分析
- **3D音盒动画**: 沉浸式的视觉体验效果
- **管理后台**: 用户、歌曲、问卷的统一管理

### 技术亮点
- **现代化前端**: React 18 + TypeScript + Vite
- **高性能后端**: Node.js + Express + Prisma ORM
- **可靠数据存储**: MySQL + Redis缓存架构
- **容器化部署**: Docker + Nginx + PM2

### 下一步计划
1. **完成前端项目搭建** - 安装依赖包和配置开发环境
2. **开发核心界面组件** - 歌曲推荐页、问卷页、音盒动画
3. **构建后端API服务** - 用户管理、推荐算法、问卷系统
4. **系统集成测试** - 前后端联调和功能验证

这个项目将为用户提供个性化的音乐推荐体验，结合精美的3D视觉效果和完善的反馈机制，打造独特的"壹人音"品牌体验。🎵✨

## 注意事项
- 重点关注音乐播放和用户体验
- 需要考虑音频文件的加载和缓存
- UI动效要流畅，避免影响性能
- 数据持久化存储用户偏好
